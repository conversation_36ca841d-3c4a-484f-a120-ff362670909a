/* 透明度网格背景测试文件 */

/* RGBA格式透明颜色 */
.rgba-transparent-1 {
    background: rgba(255, 0, 0, 0.5);
}

/* 半透明红色 */
.rgba-transparent-2 {
    background: rgba(0, 255, 0, 0.3);
}

/* 30% 透明绿色 */
.rgba-transparent-3 {
    background: rgba(0, 0, 255, 0.8);
}

/* 80% 透明蓝色 */
.rgba-transparent-4 {
    background: rgba(255, 255, 0, 0.1);
}

/* 10% 透明黄色 */
.rgba-transparent-5 {
    background: rgba(255, 0, 255, 0.9);
}

/* 90% 透明品红 */

/* HSLA格式透明颜色 */
.hsla-transparent-1 {
    color: hsla(0, 100%, 50%, 0.6);
}

/* 60% 透明红色 */
.hsla-transparent-2 {
    color: hsla(120, 100%, 50%, 0.4);
}

/* 40% 透明绿色 */
.hsla-transparent-3 {
    color: hsla(240, 100%, 50%, 0.7);
}

/* 70% 透明蓝色 */
.hsla-transparent-4 {
    color: hsla(60, 100%, 50%, 0.2);
}

/* 20% 透明黄色 */

/* ARGB十六进制格式透明颜色（8位） */
.argb-8bit-1 {
    background: #80ff0000;
}

/* 50% 透明红色 */
.argb-8bit-2 {
    background: #4000ff00;
}

/* 25% 透明绿色 */
.argb-8bit-3 {
    background: #cc0000ff;
}

/* 80% 透明蓝色 */
.argb-8bit-4 {
    background: #33ffff00;
}

/* 20% 透明黄色 */
.argb-8bit-5 {
    background: #e6ff00ff;
}

/* 90% 透明品红 */
.argb-8bit-6 {
    background: #1a00ffff;
}

/* 10% 透明青色 */

/* ARGB十六进制格式透明颜色（4位） */
.argb-4bit-1 {
    background: #8f00;
}

/* 53% 透明红色 */
.argb-4bit-2 {
    background: #40f0;
}

/* 27% 透明绿色 */
.argb-4bit-3 {
    background: #c00f;
}

/* 80% 透明蓝色 */
.argb-4bit-4 {
    background: #3ff0;
}

/* 20% 透明黄色 */
.argb-4bit-5 {
    background: #ef0f;
}

/* 93% 透明品红 */
.argb-4bit-6 {
    background: #10ff;
}

/* 7% 透明青色 */

/* 不透明颜色对比（不应该有网格背景） */
.opaque-colors-1 {
    background: #ff0000;
}

/* 不透明红色 */
.opaque-colors-2 {
    background: rgb(0, 255, 0);
}

/* 不透明绿色 */
.opaque-colors-3 {
    background: hsl(240, 100%, 50%);
}

/* 不透明蓝色 */
.opaque-colors-4 {
    background: rgba(255, 255, 0, 1);
}

/* 100% 不透明黄色 */
.opaque-colors-5 {
    background: hsla(300, 100%, 50%, 1);
}

/* 100% 不透明品红 */

/* 混合透明度测试 */
.mixed-transparency {
    color: rgba(255, 0, 0, 0.7);
    /* 透明前景色 */
    background: rgba(0, 0, 255, 0.3);
    /* 透明背景色 */
    border-color: hsla(120, 100%, 50%, 0.5);
    /* 透明边框色 */
}

/* 渐变中的透明颜色 */
.gradient-with-transparency {
    background: linear-gradient(45deg,
            rgba(255, 0, 0, 0.8) 0%,
            /* 透明红色 */
            rgba(0, 255, 0, 0.6) 50%,
            /* 透明绿色 */
            rgba(0, 0, 255, 0.4) 100%
            /* 透明蓝色 */
        );
}

/* 阴影中的透明颜色 */
.shadow-with-transparency {
    box-shadow:
        0 0 10px rgba(255, 0, 0, 0.5),
        /* 透明红色阴影 */
        0 0 20px rgba(0, 255, 0, 0.3),
        /* 透明绿色阴影 */
        0 0 30px rgba(0, 0, 255, 0.2);
    /* 透明蓝色阴影 */
}

/* 极端透明度测试 */
.very-transparent {
    background: rgba(0, 0, 0, 0.01);
}

/* 非常透明的黑色 */
.almost-opaque {
    background: rgba(255, 255, 255, 0.99);
}

/* 几乎不透明的白色 */
.half-transparent {
    background: rgba(128, 128, 128, 0.5);
}

/* 正好半透明的灰色 */

/* 说明文本 */
/* 
本文件用于测试透明度网格背景功能：
1. 所有包含透明度的颜色都应该显示白灰相间的小方格背景
2. 不透明颜色不应该显示网格背景
3. 支持多种透明颜色格式：RGBA、HSLA、ARGB十六进制
4. 网格大小为8x8像素，提供良好的视觉效果
*/