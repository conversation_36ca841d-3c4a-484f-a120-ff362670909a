<!-- markdownlint-disable MD024 -->
# 更新日志

## [未发布]

## [2.8.0]

- 支持 LCH 颜色格式
- 当变量定义颜色时支持变量语法（例如 --variable-lch, rgb, oklch 等）

## [2.7.1]

 - 更新了 node 依赖项。

## [2.7.0]

### 新增

- 支持不带 `hsl()` 的 HSL 格式以及相关配置选项（默认禁用）
- 支持 HSL 和 RGB 中的浮点数（如 `343.2 15.4% 34.4%` 或 `rgb(100.4, 89.4%, 66.4%)`）- 这在 Tailwind 中很常见

### 修复
- 修复了启用 `rgbWithNoFunction` 时函数 `rgb()` 的重复高亮问题

## [2.6.0]

### 新增

- 支持 CSS 颜色模块 4 级规范
- 添加 "useARGB" 选项以在 RGBA 和 ARGB 十六进制格式之间切换

## [2.5.0] - 2021-09-13

### 新增

- 为 Web 平台构建

## [2.4.0] - 2021-07-15

### 新增

- 工作区信任支持（在不受信任的工作区中完全支持）
- 支持空白格式
- 支持不带 rgb() 的 RGB 格式以及相关配置选项（默认禁用）
- 许可证

### 更改

- 标记类型的配置选项现在是一个选项列表

### 修复

- 修复了对比度比计算以遵循 WCAG 2.0 指南

## [2.3.0] - 2017-07-11

### 新增

- 高亮从文件导入的变量（sass, scss）
- sass 导入查找文件夹的配置选项

## [2.2.0] - 2017-05-15

### 新增

- "dot-before" 标记类型

## [2.1.3] - 2017-04-20

### 新增

- Google 表单收集用户首选设置默认值

### 修复

- 下划线样式：注释中的正确文本颜色

## [2.1.2] - 2017-04-18

### 修复

- 如果分析了上下文，则纠正高亮偏移

## [2.1.1] - 2017-04-18

### 修复

- sass、less 和 stylus 中的部分变量匹配

## [2.1.0] - 2017-04-18

### 新增

- hsl() 和 hsla() 支持
- 配置属性的描述
- 文件内的基本变量支持（对于 css、sass、less、stylus）

### 修复

- 修复非颜色上下文中的匹配，如带哈希的链接或其他地方
- 空白中的白色被着色

### 更改

- 在样式语言（css、less、scss、sass、stylus）中，颜色词匹配始终"开启"

## [2.0.1] - 2017-04-12

### 更改

- matchWords 的默认值改为 false

## [2.0.0] - 2017-04-12

### 新增

- 文档类型过滤器
- 两种新的颜色高亮样式："dot" 和 "foreground"
- 将更改列表移至 CHANGELOG.md 文件

### 更改

- 扩展在所有文档上启用
- 完全重写以获得最大性能
- 更新到最新的 vscode 库

## [1.3.2] - 0000-00-00

- 功能：添加 stylus

## [1.3.1] - 0000-00-00

- 功能：将 typescript 语言添加到列表
- 功能：添加禁用标尺中颜色的选项

## [1.3.0] - 0000-00-00

- 功能：支持十六进制透明度
- 修复：意外高亮像 "#1234567890" 这样的字符串
- 修复：高亮 Drupal PHP 代码中的非颜色数组键

## [1.2.1] - 0000-00-00

- 添加了禁用颜色词高亮的新选项

## [1.2] - 0000-00-00

- 标记的新样式模式：背景、下划线。现在默认是"背景"

## [1.1] - 0000-00-00

- 重构代码以防止内存泄漏
- 为扩展添加了配置
- 添加了高亮当前文件的命令（如果未配置为自动高亮）
