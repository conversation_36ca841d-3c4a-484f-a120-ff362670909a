# VSCode 颜色高亮插件 - 汉化和ARGB支持改进

## 改进概述

本插件是对原版 VSCode Color Highlight 插件的汉化和功能增强版本，专门针对中文用户优化，增加了ARGB格式支持，并实现了视口优化以提升性能。

## 主要改进内容

### 1. 完全汉化
- **插件名称**: 从 "Color Highlight" 改为 "颜色高亮"
- **插件描述**: 完全翻译为中文 "在编辑器中高亮显示网页颜色"
- **命令**: "Highlight colors in current file" → "在当前文件中高亮颜色"
- **配置项**: 所有配置选项的标题和描述都完整汉化
  - `enable`: "控制插件是否启用"
  - `languages`: "应该被颜色高亮插件高亮的语言ID数组"
  - `matchWords`: "在所有文件中高亮颜色词汇（灰色、绿色等）"
  - `useARGB`: "使用ARGB格式而不是RGBA格式来高亮十六进制颜色值"
  - `markerType`: "高亮样式。可选值：'dot-before'（前置圆点）、'dot-after'（后置圆点）、'foreground'（前景色）、'background'（背景色）、'outline'（轮廓）、'underline'（下划线）"
  - 等等...

### 2. ARGB格式支持增强

#### 支持的ARGB格式
- **8位十六进制**: `#AARRGGBB` (如: `#80ff0000` = 半透明红色)
- **4位十六进制**: `#ARGB` (如: `#8f00` = 半透明红色)

#### 技术实现
- 修复了原版插件中ARGB格式的正则表达式错误
- 正确解析8位和4位ARGB格式颜色
- 将ARGB颜色转换为CSS兼容的rgba()格式以便VSCode正确显示
- 默认启用ARGB模式（`useARGB: true`）

#### ARGB颜色解析逻辑
- **8位格式** (`#AARRGGBB`): 前两位为alpha通道 (00-FF)
- **4位格式** (`#ARGB`): 第一位为alpha通道 (0-F)，每位扩展为两位
- Alpha通道转换: `0x80 → 0.5`, `0xFF → 1.0`, `0x00 → 0.0`

### 3. 视口优化 (新增)

#### 问题背景
用户反馈在包含大量颜色的文件中滚动时会出现严重的页面卡顿问题。

#### 解决方案
实现了类似于 preview-image-zh 插件的视口优化技术，只高亮当前屏幕可见区域内的颜色。

#### 技术特性
- **智能视口检测**: 只处理编辑器可见区域的颜色（包含5行缓冲区）
- **动态更新**: 监听滚动事件，实时更新可见颜色
- **性能优化**: 
  - 防抖机制（滚动300ms后更新，视口变化200ms后更新）
  - 批量装饰器更新
  - 使用 `requestAnimationFrame` 避免阻塞UI
  - 缓存全文颜色解析结果
- **无数量限制**: 移除了之前的颜色数量限制，所有颜色都能显示

#### 具体实现
1. **全文解析**: 文档变化时解析所有颜色并缓存
2. **视口过滤**: 滚动时只从缓存中过滤出可见区域的颜色
3. **分离更新**: 文档内容变化触发完整解析，视口变化只触发过滤更新
4. **事件监听**: 添加 `onDidChangeTextEditorVisibleRanges` 监听器

### 4. 性能配置保留

#### 文件大小限制
- **配置项**: `maxFileSize` (默认500KB)
- **作用**: 超过此大小的文件将跳过颜色高亮处理
- **目的**: 防止在超大文件中造成性能问题

#### 配置移除
- 移除了 `maxColorsPerFile` 配置项，因为视口优化已解决性能问题

### 5. 测试文件

#### test-viewport.css
- 包含200行，超过300个颜色定义
- 覆盖各种颜色格式：RGB、RGBA、HSL、HSLA、十六进制、ARGB
- 用于测试视口优化的滚动性能

#### test-argb.css
- 专门测试ARGB格式颜色的解析和显示
- 包含8位和4位ARGB格式示例

#### test-xml-colors.xml
- 测试XML文件中的颜色高亮
- 验证跨语言支持

## 版本历史

### v2.8.0 - 视口优化版
- ✅ 实现视口优化，解决大量颜色文件的滚动卡顿问题
- ✅ 移除颜色数量限制，所有颜色都能正确显示
- ✅ 添加智能缓存机制
- ✅ 优化防抖和批量更新策略

### v2.7.0 - 性能优化版
- ✅ 添加颜色数量限制以提高性能
- ✅ 实现防抖机制减少频繁更新
- ✅ 批量装饰器管理和LRU缓存

### v2.6.0 - ARGB默认版
- ✅ 将ARGB格式设为默认启用
- ✅ 优化ARGB颜色解析和显示

### v2.5.0 - 修复版
- ✅ 修复ARGB格式的正则表达式
- ✅ 改进颜色转换逻辑

### v2.0.0 - 汉化版
- ✅ 完全汉化界面和配置
- ✅ 添加ARGB格式支持
- ✅ 保持原有功能兼容性

## 安装和使用

1. 在VSCode中安装本插件的 `.vsix` 文件
2. 插件会自动启用并开始高亮颜色
3. 可通过设置调整各种配置选项
4. 使用命令面板执行 "在当前文件中高亮颜色" 手动触发高亮

## 配置说明

在VSCode设置中搜索 "颜色高亮" 或 "color-highlight" 可找到所有配置选项：

- **启用**: 控制插件总开关
- **支持语言**: 配置在哪些语言中启用高亮
- **ARGB格式**: 推荐保持启用以支持透明度颜色
- **高亮样式**: 可选择不同的高亮显示方式
- **文件大小限制**: 防止大文件性能问题

## 技术细节

- **基于原版**: VSCode Color Highlight v2.8.0
- **兼容性**: 支持VSCode 1.57.0+
- **性能**: 通过视口优化技术大幅提升大文件性能
- **维护性**: 保持与原版API兼容，便于后续更新

## 已知问题和限制

1. **文件大小**: 默认限制500KB，超过的文件不会高亮
2. **颜色格式**: 部分非标准颜色格式可能不被识别
3. **性能**: 在极端情况下（上万个颜色）仍可能有轻微延迟

## 反馈和建议

如发现问题或有改进建议，请通过以下方式反馈：
- GitHub Issues
- VSCode插件评论区
- 直接联系开发者

感谢使用颜色高亮插件！ 