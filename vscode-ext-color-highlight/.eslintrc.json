{"parser": "@babel/eslint-parser", "env": {"browser": true, "commonjs": true, "es6": true, "node": true}, "globals": {"requestAnimationFrame": "readonly"}, "parserOptions": {"sourceType": "module", "allowImportExportEverywhere": false, "codeFrame": false, "requireConfigFile": false}, "rules": {"no-const-assign": "warn", "no-this-before-super": "warn", "no-undef": "warn", "no-unreachable": "warn", "no-unused-vars": "warn", "constructor-super": "warn", "valid-typeof": "warn", "semi": "warn", "quotes": ["warn", "single"]}}