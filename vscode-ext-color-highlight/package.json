{"name": "color-highlight", "displayName": "颜色高亮", "description": "在编辑器中高亮显示网页颜色", "version": "2.8.0", "publisher": "<PERSON>um<PERSON><PERSON>", "license": "GPL-3.0", "engines": {"vscode": "^1.57.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "capabilities": {"untrustedWorkspaces": {"supported": true}}, "scripts": {"build": "webpack --mode development", "vscode:prepublish": "webpack --mode production", "dev": "webpack --mode development -w", "force-resolutions": "npx npm-force-resolutions", "test": "echo 'Tests skipped for now'"}, "bugs": {"url": "https://github.com/naumovs/vscode-ext-color-highlight/issues"}, "homepage": "https://github.com/naumovs/vscode-ext-color-highlight", "repository": {"type": "git", "url": "https://github.com/naumovs/vscode-ext-color-highlight.git"}, "browser": "./dist/extension-web.js", "main": "./dist/extension-node.js", "icon": "images/preview.png", "contributes": {"configuration": {"title": "颜色高亮", "properties": {"color-highlight.enable": {"default": true, "description": "控制插件是否启用", "type": "boolean"}, "color-highlight.languages": {"default": ["*"], "description": "应该被颜色高亮插件高亮的语言ID数组。使用 \"*\" 匹配所有语言；在语言ID前加 \"!\" 排除该语言（例如 \"!typescript\", \"!javascript\"）", "type": "array"}, "color-highlight.matchWords": {"default": false, "description": "在所有文件中高亮颜色词汇（灰色、绿色等）", "type": "boolean"}, "color-highlight.useARGB": {"default": true, "description": "使用ARGB格式而不是RGBA格式来高亮十六进制颜色值", "type": "boolean"}, "color-highlight.matchRgbWithNoFunction": {"default": false, "description": "高亮不带函数的RGB格式（如 '255, 255, 255'、[255, 255, 255]、'255 255 255' 等）", "type": "boolean"}, "color-highlight.rgbWithNoFunctionLanguages": {"default": ["*"], "description": "应该使用无函数RGB模式高亮的语言ID数组。使用 \"*\" 匹配所有语言；在语言ID前加 \"!\" 排除该语言（例如 \"!typescript\", \"!javascript\"）", "type": "array"}, "color-highlight.matchHslWithNoFunction": {"default": false, "description": "高亮不带函数的HSL格式（如 '255, 100%, 80%'、[255, 100%, 80%]、'255 100% 80%' 等）", "type": "boolean"}, "color-highlight.hslWithNoFunctionLanguages": {"default": ["*"], "description": "应该使用无函数HSL模式高亮的语言ID数组。使用 \"*\" 匹配所有语言；在语言ID前加 \"!\" 排除该语言（例如 \"!typescript\", \"!javascript\"）", "type": "array"}, "color-highlight.markerType": {"default": "background", "description": "高亮样式。可选值：'dot-before'（前置圆点）、'dot-after'（后置圆点）、'foreground'（前景色）、'background'（背景色）、'outline'（轮廓）、'underline'（下划线）", "type": "string", "enum": ["dot-before", "dot-after", "foreground", "background", "outline", "underline"]}, "color-highlight.markRuler": {"default": true, "description": "在标尺（滚动条）上高亮颜色，true/false", "type": "boolean"}, "color-highlight.sass.includePaths": {"default": [], "description": "执行文件查找时要搜索的绝对路径数组", "type": "array"}, "color-highlight.maxFileSize": {"default": 500, "description": "最大处理文件大小（KB）。超过此大小的文件将跳过颜色高亮以提高性能", "type": "number", "minimum": 100, "maximum": 5000}}}, "commands": [{"command": "extension.colorHighlight", "title": "在当前文件中高亮颜色"}]}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/eslint-parser": "^7.14.7", "@babel/plugin-transform-runtime": "^7.14.5", "@babel/preset-env": "^7.14.7", "@types/mocha": "^2.2.32", "@types/node": "^6.0.40", "@types/vscode": "^1.57.0", "babel-loader": "^8.2.2", "eslint": "^7.29.0", "mocha": "^9.0.0", "npm-force-resolutions": "0.0.10", "path-browserify": "^1.0.1", "webpack": "^5.76.0", "webpack-cli": "^4.7.2"}, "dependencies": {"@babel/runtime": "^7.14.6", "color": "^1.0.3", "color-name": "^1.1.4", "file-importer": "^1.0.0"}, "resolutions": {"minimist": "^1.2.5"}}