# [vscode-ext-color-highlight](https://github.com/naumovs/vscode-ext-color-highlight)

这个扩展插件可以为文档中发现的 CSS/网页颜色添加样式。

## 安装

在 VSCode 中按 Ctrl+Shift+P（Mac 系统按 Cmd+Shift+P），然后输入 ">ext install"，按回车，搜索 "Color Highlight"。

还有疑问？点击上方的"Get Started"。

## 问卷调查

请回答此问卷中的问题。您的反馈非常宝贵，将帮助我改进这个扩展插件。
[https://goo.gl/forms/5emac4WyQv7CWZOK2](https://goo.gl/forms/5emac4WyQv7CWZOK2)

## 贡献者

- [chadgauth](https://github.com/chadgauth) - 支持 LCH 颜色格式
- [LucasMatuszewski](https://github.com/LucasMatuszewski) - 支持 HSL 和 RGB 中的浮点数
- [lochstar](https://github.com/lochstar) - 标记的样式模式

欢迎贡献！
