<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 标准RGB颜色 -->
    <color name="red">#FF0000</color>
    <color name="green">#00FF00</color>
    <color name="blue">#0000FF</color>

    <!-- 8位ARGB格式颜色 (当启用ARGB模式时) -->
    <color name="transparent_red">#80FF0000</color>
    <color name="transparent_green">#8000FF00</color>
    <color name="transparent_blue">#000000FF</color>

    <!-- 完全不透明的ARGB格式 -->
    <color name="opaque_red">#FFFF0000</color>
    <color name="opaque_green">#FF00FF00</color>
    <color name="opaque_blue">#FF0000FF</color>

    <!-- 4位ARGB格式 -->
    <color name="semi_red">#8F00</color>
    <color name="semi_green">#80F0</color>
    <color name="semi_blue">#800F</color>

    <!-- 3位RGB格式 -->
    <color name="short_red">#F00</color>
    <color name="short_green">#0F0</color>
    <color name="short_blue">#00F</color>

    <!-- Android系统常用的半透明颜色 -->
    <color name="overlay_dark">#80000000</color>
    <color name="overlay_light">#80FFFFFF</color>

    <!-- 品牌色示例 -->
    <color name="brand_primary">#FF2196F3</color>
    <color name="brand_accent">#FFFF5722</color>

    <!-- 透明度渐变 -->
    <color name="fade_25">#40FF0000</color> <!-- 25% 透明度 -->
    <color name="fade_50">#80FF0000</color> <!-- 50% 透明度 -->
    <color name="fade_75">#C0FF0000</color> <!-- 75% 透明度 -->

</resources>