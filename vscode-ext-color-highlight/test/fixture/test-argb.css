/* ARGB 格式测试文件 */

/* 4位ARGB格式测试（启用ARGB模式时，这些颜色将按ARGB格式解析） */
.test-argb-4 {
    /* 完全不透明的红色 #FF00 -> #FFFF0000 */
    color: #FF00;

    /* 半透明的绿色 #80F0 -> #8800FF00 */
    background: #80F0;

    /* 几乎透明的蓝色 #100F -> #110000FF */
    border-color: #100F;
}

/* 8位ARGB格式测试（启用ARGB模式时，直接使用ARGB格式） */
.test-argb-8 {
    /* 完全不透明的红色 #FFFF0000 (A=FF, R=FF, G=00, B=00) */
    color: #FFFF0000;

    /* 半透明的绿色 #8000FF00 (A=80, R=00, G=FF, B=00) */
    background: #8000FF00;

    /* 25%透明度的蓝色 #400000FF (A=40, R=00, G=00, B=FF) */
    border-color: #400000FF;

    /* 75%透明度的黄色 #C0FFFF00 (A=C0, R=FF, G=FF, B=00) */
    box-shadow: 0 0 10px #C0FFFF00;
}

/* 混合测试 */
.test-mixed {
    /* 标准RGB */
    color: #FF0000;

    /* 4位ARGB */
    background: #8F00;

    /* 8位ARGB */
    border: 1px solid #80FF0000;

    /* RGBA函数 */
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
}

/* 常见的ARGB颜色 */
.common-argb {
    /* Android常用的半透明黑色 */
    background: #80000000;

    /* 半透明白色 */
    color: #80FFFFFF;

    /* 透明的品牌色 */
    border-color: #4D007AFF;
}