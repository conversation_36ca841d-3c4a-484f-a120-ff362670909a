/* 性能测试文件 - 包含大量颜色值 */

:root {
    /* 基础色板 */
    --color-primary: #FF0000;
    --color-secondary: #00FF00;
    --color-tertiary: #0000FF;
    --color-quaternary: #FFFF00;
    --color-quinary: #FF00FF;
    --color-senary: #00FFFF;

    /* 灰度色板 */
    --gray-100: #F8F9FA;
    --gray-200: #E9ECEF;
    --gray-300: #DEE2E6;
    --gray-400: #CED4DA;
    --gray-500: #ADB5BD;
    --gray-600: #6C757D;
    --gray-700: #495057;
    --gray-800: #343A40;
    --gray-900: #212529;

    /* 语义化颜色 */
    --success: #28A745;
    --info: #17A2B8;
    --warning: #FFC107;
    --danger: #DC3545;
    --light: #F8F9FA;
    --dark: #343A40;

    /* ARGB格式颜色 */
    --transparent-red: #80FF0000;
    --transparent-green: #8000FF00;
    --transparent-blue: #800000FF;
    --transparent-black: #80000000;
    --transparent-white: #80FFFFFF;
}

/* 大量CSS类，每个都包含多个颜色属性 */
.color-test-1 {
    color: #FF0000;
    background: #00FF00;
    border-color: #0000FF;
}

.color-test-2 {
    color: #FF1111;
    background: #11FF11;
    border-color: #1111FF;
}

.color-test-3 {
    color: #FF2222;
    background: #22FF22;
    border-color: #2222FF;
}

.color-test-4 {
    color: #FF3333;
    background: #33FF33;
    border-color: #3333FF;
}

.color-test-5 {
    color: #FF4444;
    background: #44FF44;
    border-color: #4444FF;
}

.color-test-6 {
    color: #FF5555;
    background: #55FF55;
    border-color: #5555FF;
}

.color-test-7 {
    color: #FF6666;
    background: #66FF66;
    border-color: #6666FF;
}

.color-test-8 {
    color: #FF7777;
    background: #77FF77;
    border-color: #7777FF;
}

.color-test-9 {
    color: #FF8888;
    background: #88FF88;
    border-color: #8888FF;
}

.color-test-10 {
    color: #FF9999;
    background: #99FF99;
    border-color: #9999FF;
}

.rgba-test-1 {
    background: rgba(255, 0, 0, 0.1);
}

.rgba-test-2 {
    background: rgba(0, 255, 0, 0.2);
}

.rgba-test-3 {
    background: rgba(0, 0, 255, 0.3);
}

.rgba-test-4 {
    background: rgba(255, 255, 0, 0.4);
}

.rgba-test-5 {
    background: rgba(255, 0, 255, 0.5);
}

.hsla-test-1 {
    background: hsla(0, 100%, 50%, 0.1);
}

.hsla-test-2 {
    background: hsla(120, 100%, 50%, 0.2);
}

.hsla-test-3 {
    background: hsla(240, 100%, 50%, 0.3);
}

.hsla-test-4 {
    background: hsla(60, 100%, 50%, 0.4);
}

.hsla-test-5 {
    background: hsla(300, 100%, 50%, 0.5);
}

/* 更多颜色测试 */
.gradient-1 {
    background: linear-gradient(45deg, #FF0000, #00FF00, #0000FF, #FFFF00, #FF00FF, #00FFFF);
}

.gradient-2 {
    background: radial-gradient(circle, #FF1100, #00FF11, #1100FF, #FFFF11, #FF11FF, #11FFFF);
}

.shadow-test-1 {
    box-shadow: 0 0 10px #FF0000, 0 0 20px #00FF00, 0 0 30px #0000FF;
}

.shadow-test-2 {
    text-shadow: 1px 1px #FF0000, 2px 2px #00FF00, 3px 3px #0000FF;
}

/* ARGB格式测试 */
.argb-1 {
    color: #FFFF0000;
}

/* 完全不透明红色 */
.argb-2 {
    color: #FF00FF00;
}

/* 完全不透明绿色 */
.argb-3 {
    color: #FF0000FF;
}

/* 完全不透明蓝色 */
.argb-4 {
    color: #80FF0000;
}

/* 半透明红色 */
.argb-5 {
    color: #8000FF00;
}

/* 半透明绿色 */
.argb-6 {
    color: #800000FF;
}

/* 半透明蓝色 */
.argb-7 {
    color: #40FF0000;
}

/* 25%透明红色 */
.argb-8 {
    color: #4000FF00;
}

/* 25%透明绿色 */
.argb-9 {
    color: #400000FF;
}

/* 25%透明蓝色 */
.argb-10 {
    color: #C0FF0000;
}

/* 75%透明红色 */

/* 4位ARGB */
.argb-short-1 {
    color: #FF00;
}

.argb-short-2 {
    color: #F0F0;
}

.argb-short-3 {
    color: #F00F;
}

.argb-short-4 {
    color: #8F00;
}

.argb-short-5 {
    color: #80F0;
}

/* 品牌色系列 */
.brand-primary-100 {
    color: #E3F2FD;
}

.brand-primary-200 {
    color: #BBDEFB;
}

.brand-primary-300 {
    color: #90CAF9;
}

.brand-primary-400 {
    color: #64B5F6;
}

.brand-primary-500 {
    color: #2196F3;
}

.brand-primary-600 {
    color: #1E88E5;
}

.brand-primary-700 {
    color: #1976D2;
}

.brand-primary-800 {
    color: #1565C0;
}

.brand-primary-900 {
    color: #0D47A1;
}

.accent-100 {
    color: #FFF3E0;
}

.accent-200 {
    color: #FFE0B2;
}

.accent-300 {
    color: #FFCC80;
}

.accent-400 {
    color: #FFB74D;
}

.accent-500 {
    color: #FF9800;
}

.accent-600 {
    color: #FB8C00;
}

.accent-700 {
    color: #F57C00;
}

.accent-800 {
    color: #EF6C00;
}

.accent-900 {
    color: #E65100;
}

/* 语义化状态颜色 */
.success-light {
    color: #D4EDDA;
    background: #C3E6CB;
}

.success-normal {
    color: #155724;
    background: #28A745;
}

.success-dark {
    color: #0F3B1C;
    background: #1E7E34;
}

.error-light {
    color: #F8D7DA;
    background: #F5C6CB;
}

.error-normal {
    color: #721C24;
    background: #DC3545;
}

.error-dark {
    color: #491217;
    background: #C82333;
}

.warning-light {
    color: #FFF3CD;
    background: #FFEAA7;
}

.warning-normal {
    color: #856404;
    background: #FFC107;
}

.warning-dark {
    color: #533F03;
    background: #E0A800;
}

.info-light {
    color: #CCE7F0;
    background: #BEE5EB;
}

.info-normal {
    color: #0C5460;
    background: #17A2B8;
}

.info-dark {
    color: #062C33;
    background: #138496;
}