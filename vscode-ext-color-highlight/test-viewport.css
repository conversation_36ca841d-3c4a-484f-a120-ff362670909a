/* 视口优化测试文件 - 包含大量颜色用于测试滚动性能 */

/* 第一部分：基础颜色 (1-30行) */
.color-block-1 {
    background: #ff0000;
}

.color-block-2 {
    background: #00ff00;
}

.color-block-3 {
    background: #0000ff;
}

.color-block-4 {
    background: #ffff00;
}

.color-block-5 {
    background: #ff00ff;
}

.color-block-6 {
    background: #00ffff;
}

.color-block-7 {
    background: #800000;
}

.color-block-8 {
    background: #008000;
}

.color-block-9 {
    background: #000080;
}

.color-block-10 {
    background: #808000;
}

.color-block-11 {
    background: #800080;
}

.color-block-12 {
    background: #008080;
}

.color-block-13 {
    background: #c0c0c0;
}

.color-block-14 {
    background: #808080;
}

.color-block-15 {
    background: #9999ff;
}

.color-block-16 {
    background: #993366;
}

.color-block-17 {
    background: #ffffcc;
}

.color-block-18 {
    background: #ccffff;
}

.color-block-19 {
    background: #660066;
}

.color-block-20 {
    background: #ff8080;
}

.color-block-21 {
    background: #0066cc;
}

.color-block-22 {
    background: #ccccff;
}

.color-block-23 {
    background: #000000;
}

.color-block-24 {
    background: #ffffff;
}

.color-block-25 {
    background: #ffcccc;
}

.color-block-26 {
    background: #ccffcc;
}

.color-block-27 {
    background: #ccccff;
}

.color-block-28 {
    background: #ffffcc;
}

.color-block-29 {
    background: #ffccff;
}

.color-block-30 {
    background: #ccffff;
}

/* 第二部分：RGB和RGBA颜色 (31-60行) */
.rgb-color-1 {
    color: rgb(255, 0, 0);
}

.rgb-color-2 {
    color: rgb(0, 255, 0);
}

.rgb-color-3 {
    color: rgb(0, 0, 255);
}

.rgb-color-4 {
    color: rgb(255, 255, 0);
}

.rgb-color-5 {
    color: rgb(255, 0, 255);
}

.rgb-color-6 {
    color: rgb(0, 255, 255);
}

.rgba-color-1 {
    color: rgba(255, 0, 0, 0.5);
}

.rgba-color-2 {
    color: rgba(0, 255, 0, 0.5);
}

.rgba-color-3 {
    color: rgba(0, 0, 255, 0.5);
}

.rgba-color-4 {
    color: rgba(255, 255, 0, 0.8);
}

.rgba-color-5 {
    color: rgba(255, 0, 255, 0.3);
}

.rgba-color-6 {
    color: rgba(0, 255, 255, 0.7);
}

.rgba-color-7 {
    color: rgba(128, 128, 128, 0.9);
}

.rgba-color-8 {
    color: rgba(192, 192, 192, 0.6);
}

.rgba-color-9 {
    color: rgba(64, 64, 64, 0.4);
}

.rgba-color-10 {
    color: rgba(32, 32, 32, 0.2);
}

.rgba-color-11 {
    color: rgba(224, 224, 224, 0.85);
}

.rgba-color-12 {
    color: rgba(160, 160, 160, 0.65);
}

.rgba-color-13 {
    color: rgba(96, 96, 96, 0.45);
}

.rgba-color-14 {
    color: rgba(48, 48, 48, 0.25);
}

.rgba-color-15 {
    color: rgba(240, 240, 240, 0.95);
}

.rgba-color-16 {
    color: rgba(208, 208, 208, 0.75);
}

.rgba-color-17 {
    color: rgba(176, 176, 176, 0.55);
}

.rgba-color-18 {
    color: rgba(144, 144, 144, 0.35);
}

.rgba-color-19 {
    color: rgba(112, 112, 112, 0.15);
}

.rgba-color-20 {
    color: rgba(80, 80, 80, 0.05);
}

.rgb-mix-1 {
    background: rgb(200, 100, 50);
}

.rgb-mix-2 {
    background: rgb(150, 200, 75);
}

.rgb-mix-3 {
    background: rgb(75, 150, 200);
}

.rgb-mix-4 {
    background: rgb(180, 90, 120);
}

/* 第三部分：HSL和HSLA颜色 (61-90行) */
.hsl-color-1 {
    color: hsl(0, 100%, 50%);
}

.hsl-color-2 {
    color: hsl(120, 100%, 50%);
}

.hsl-color-3 {
    color: hsl(240, 100%, 50%);
}

.hsl-color-4 {
    color: hsl(60, 100%, 50%);
}

.hsl-color-5 {
    color: hsl(300, 100%, 50%);
}

.hsl-color-6 {
    color: hsl(180, 100%, 50%);
}

.hsla-color-1 {
    color: hsla(0, 100%, 50%, 0.5);
}

.hsla-color-2 {
    color: hsla(120, 100%, 50%, 0.5);
}

.hsla-color-3 {
    color: hsla(240, 100%, 50%, 0.5);
}

.hsla-color-4 {
    color: hsla(60, 100%, 50%, 0.8);
}

.hsla-color-5 {
    color: hsla(300, 100%, 50%, 0.3);
}

.hsla-color-6 {
    color: hsla(180, 100%, 50%, 0.7);
}

.hsl-shade-1 {
    background: hsl(15, 80%, 60%);
}

.hsl-shade-2 {
    background: hsl(30, 70%, 55%);
}

.hsl-shade-3 {
    background: hsl(45, 90%, 65%);
}

.hsl-shade-4 {
    background: hsl(90, 60%, 70%);
}

.hsl-shade-5 {
    background: hsl(135, 85%, 45%);
}

.hsl-shade-6 {
    background: hsl(210, 75%, 40%);
}

.hsl-shade-7 {
    background: hsl(270, 65%, 55%);
}

.hsl-shade-8 {
    background: hsl(315, 95%, 35%);
}

.hsl-shade-9 {
    background: hsl(345, 55%, 75%);
}

.hsl-shade-10 {
    background: hsl(165, 45%, 65%);
}

.hsl-shade-11 {
    background: hsl(195, 85%, 25%);
}

.hsl-shade-12 {
    background: hsl(225, 75%, 85%);
}

.hsl-shade-13 {
    background: hsl(255, 35%, 45%);
}

.hsl-shade-14 {
    background: hsl(285, 65%, 30%);
}

.hsl-shade-15 {
    background: hsl(75, 95%, 80%);
}

/* 第四部分：ARGB 十六进制颜色 (91-120行) */
.argb-color-1 {
    background: #80ff0000;
    /* 半透明红色 */
}

.argb-color-2 {
    background: #8000ff00;
    /* 半透明绿色 */
}

.argb-color-3 {
    background: #800000ff;
    /* 半透明蓝色 */
}

.argb-color-4 {
    background: #ff123456;
    /* 不透明深色 */
}

.argb-color-5 {
    background: #cc789abc;
    /* 部分透明浅色 */
}

.argb-color-6 {
    background: #66def012;
    /* 高透明度黄绿色 */
}

.argb-color-7 {
    background: #aa34567a;
    /* 中等透明度 */
}

.argb-color-8 {
    background: #eeabcdef;
    /* 低透明度浅色 */
}

.argb-color-9 {
    background: #22fedcba;
    /* 高透明度 */
}

.argb-color-10 {
    background: #dd987654;
    /* 低透明度深色 */
}

.argb-short-1 {
    background: #8f00;
    /* 4位ARGB 半透明红色 */
}

.argb-short-2 {
    background: #80f0;
    /* 4位ARGB 半透明绿色 */
}

.argb-short-3 {
    background: #800f;
    /* 4位ARGB 半透明蓝色 */
}

.argb-short-4 {
    background: #afff;
    /* 4位ARGB 半透明白色 */
}

.argb-short-5 {
    background: #a000;
    /* 4位ARGB 半透明黑色 */
}

.argb-short-6 {
    background: #c123;
    /* 4位ARGB 中等透明度 */
}

.argb-short-7 {
    background: #e456;
    /* 4位ARGB 低透明度 */
}

.argb-short-8 {
    background: #6789;
    /* 4位ARGB 高透明度 */
}

.argb-short-9 {
    background: #fabc;
    /* 4位ARGB 很低透明度 */
}

.argb-short-10 {
    background: #3def;
    /* 4位ARGB 很高透明度 */
}

.hex-normal-1 {
    background: #ff5733;
}

.hex-normal-2 {
    background: #33ff57;
}

.hex-normal-3 {
    background: #3357ff;
}

.hex-normal-4 {
    background: #f39c12;
}

.hex-normal-5 {
    background: #9b59b6;
}

.hex-normal-6 {
    background: #1abc9c;
}

.hex-normal-7 {
    background: #e74c3c;
}

.hex-normal-8 {
    background: #2ecc71;
}

.hex-normal-9 {
    background: #3498db;
}

/* 第五部分：混合颜色格式 (121-150行) */
.mixed-colors-1 {
    color: #ff0000;
    background: rgba(0, 255, 0, 0.5);
    border-color: hsl(240, 100%, 50%);
}

.mixed-colors-2 {
    color: rgb(255, 165, 0);
    background: #8000ffff;
    border-color: hsla(120, 50%, 60%, 0.8);
}

.mixed-colors-3 {
    color: #663399;
    background: rgba(255, 192, 203, 0.6);
    border-color: hsl(30, 100%, 50%);
}

.complex-gradient {
    background: linear-gradient(45deg,
            #ff6b6b 0%,
            #4ecdc4 25%,
            #45b7d1 50%,
            #96ceb4 75%,
            #feca57 100%);
}

.multi-shadow {
    box-shadow:
        0 0 10px rgba(255, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 0, 0.3),
        0 0 30px rgba(0, 0, 255, 0.2);
}

.animation-colors {
    animation: colorChange 3s infinite;
}

@keyframes colorChange {
    0% {
        background: #ff0000;
    }

    33% {
        background: #00ff00;
    }

    66% {
        background: #0000ff;
    }

    100% {
        background: #ff0000;
    }
}

/* 第六部分：更多测试颜色 (151-200行) */
.extra-color-1 {
    background: #ff1744;
}

.extra-color-2 {
    background: #f50057;
}

.extra-color-3 {
    background: #d500f9;
}

.extra-color-4 {
    background: #651fff;
}

.extra-color-5 {
    background: #3d5afe;
}

.extra-color-6 {
    background: #2979ff;
}

.extra-color-7 {
    background: #00b0ff;
}

.extra-color-8 {
    background: #00e5ff;
}

.extra-color-9 {
    background: #1de9b6;
}

.extra-color-10 {
    background: #00e676;
}

.extra-color-11 {
    background: #76ff03;
}

.extra-color-12 {
    background: #c6ff00;
}

.extra-color-13 {
    background: #ffea00;
}

.extra-color-14 {
    background: #ffc400;
}

.extra-color-15 {
    background: #ff9100;
}

.extra-color-16 {
    background: #ff3d00;
}

.extra-color-17 {
    background: #dd2c00;
}

.extra-color-18 {
    background: #d50000;
}

.extra-color-19 {
    background: #c62828;
}

.extra-color-20 {
    background: #ad1457;
}

.extra-rgb-1 {
    color: rgb(233, 30, 99);
}

.extra-rgb-2 {
    color: rgb(156, 39, 176);
}

.extra-rgb-3 {
    color: rgb(103, 58, 183);
}

.extra-rgb-4 {
    color: rgb(63, 81, 181);
}

.extra-rgb-5 {
    color: rgb(33, 150, 243);
}

.extra-rgb-6 {
    color: rgb(3, 169, 244);
}

.extra-rgb-7 {
    color: rgb(0, 188, 212);
}

.extra-rgb-8 {
    color: rgb(0, 150, 136);
}

.extra-rgb-9 {
    color: rgb(76, 175, 80);
}

.extra-rgb-10 {
    color: rgb(139, 195, 74);
}

.extra-hsl-1 {
    color: hsl(340, 82%, 52%);
}

.extra-hsl-2 {
    color: hsl(291, 64%, 42%);
}

.extra-hsl-3 {
    color: hsl(262, 52%, 47%);
}

.extra-hsl-4 {
    color: hsl(231, 48%, 48%);
}

.extra-hsl-5 {
    color: hsl(207, 90%, 54%);
}

.extra-hsl-6 {
    color: hsl(199, 98%, 48%);
}

.extra-hsl-7 {
    color: hsl(187, 100%, 42%);
}

.extra-hsl-8 {
    color: hsl(174, 100%, 29%);
}

.extra-hsl-9 {
    color: hsl(122, 39%, 49%);
}

.extra-hsl-10 {
    color: hsl(88, 50%, 53%);
}

.extra-argb-1 {
    background: #b3e91e63;
}

.extra-argb-2 {
    background: #cc9c27b0;
}

.extra-argb-3 {
    background: #d5673ab7;
}

.extra-argb-4 {
    background: #e63f51b5;
}

.extra-argb-5 {
    background: #f52196f3;
}

.extra-argb-6 {
    background: #aa03a9f4;
}

.extra-argb-7 {
    background: #bb00bcd4;
}

.extra-argb-8 {
    background: #cc009688;
}

.extra-argb-9 {
    background: #dd4caf50;
}

.extra-argb-10 {
    background: #ee8bc34a;
}

/* 结尾：总结信息 */
/* 本文件包含约200行，超过300个颜色定义用于测试视口优化功能 */
/* 在启用视口优化后，只有当前可见区域的颜色会被高亮显示 */
/* 这样可以大大提高滚动性能，避免页面卡顿 */