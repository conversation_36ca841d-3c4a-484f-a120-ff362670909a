'use strict';
import {
    workspace,
    window,
    Range,
} from 'vscode';
import { findScssVars } from './strategies/scss-vars';
import { findLessVars } from './strategies/less-vars';
import { findStylVars } from './strategies/styl-vars';
import { findCssVars } from './strategies/css-vars';
import { findColorFunctionsInText } from './strategies/functions';
import { findRgbNoFn } from './strategies/rgbWithoutFunction';
import { findHslNoFn } from './strategies/hslWithoutFunction';
import { findHexARGB, findHexRGBA } from './strategies/hex';
import { findHwb } from './strategies/hwb';
import { findWords } from './strategies/words';
import { DecorationMap } from './lib/decoration-map';
import { dirname } from 'path';

const colorWordsLanguages = [ 'css', 'scss', 'sass', 'less', 'stylus' ];

// 性能优化常量
const DEBOUNCE_DELAY = 300; // 防抖延迟（毫秒）
const VIEWPORT_DEBOUNCE_DELAY = 200; // 视口变化防抖延迟（毫秒）

export class DocumentHighlight {

    /**
     * Creates an instance of DocumentHighlight.
     * @param {TextDocument} document
     * @param {any} viewConfig
     *
     * @memberOf DocumentHighlight
     */
    constructor(document, viewConfig) {
        this.disposed = false;
        this.updateTimeout = null; // 防抖定时器
        this.viewportTimeout = null; // 视口变化防抖定时器
        this.viewConfig = viewConfig; // 保存配置以便后续使用
        this.allColorResults = null; // 缓存所有颜色结果
        this.lastTextVersion = null; // 记录最后一次解析的文本版本

        this.document = document;
        this.strategies = [ findColorFunctionsInText, findHwb ];

        if (viewConfig.useARGB == true) {
            this.strategies.push(findHexARGB);
        } else {
            this.strategies.push(findHexRGBA);
        }

        if (colorWordsLanguages.indexOf(document.languageId) > -1 || viewConfig.matchWords) {
            this.strategies.push(findWords);
        }

        if (viewConfig.matchRgbWithNoFunction) {
            let isValid = false;

            if (viewConfig.rgbWithNoFunctionLanguages.indexOf('*') > -1) {
                isValid = true;
            }

            if (viewConfig.rgbWithNoFunctionLanguages.indexOf(document.languageId) > -1) {
                isValid = true;
            }

            if (viewConfig.rgbWithNoFunctionLanguages.indexOf(`!${document.languageId}`) > -1) {
                isValid = false;
            }

            if (isValid) this.strategies.push(findRgbNoFn);
        }

        if (viewConfig.matchHslWithNoFunction) {
            let isValid = false;

            if (viewConfig.hslWithNoFunctionLanguages.indexOf('*') > -1) {
                isValid = true;
            }

            if (viewConfig.hslWithNoFunctionLanguages.indexOf(document.languageId) > -1) {
                isValid = true;
            }

            if (viewConfig.hslWithNoFunctionLanguages.indexOf(`!${document.languageId}`) > -1) {
                isValid = false;
            }

            if (isValid) this.strategies.push(findHslNoFn);
        }

        switch (document.languageId) {
            case 'css':
                this.strategies.push(findCssVars);
                break;
            case 'less':
                this.strategies.push(findLessVars);
                break;
            case 'stylus':
                this.strategies.push(findStylVars);
                break;
            case 'sass':
            case 'scss':
                this.strategies.push(text => findScssVars(text, {
                    data: text,
                    cwd: dirname(document.uri.fsPath),
                    extensions: [ '.scss', '.sass' ],
                    includePaths: viewConfig.sass.includePaths || []
                }));
                break;
        }

        this.initialize(viewConfig);
    }

    initialize(viewConfig) {
        this.decorations = new DecorationMap(viewConfig);
        this.listner = workspace.onDidChangeTextDocument(({ document }) => this.onUpdate(document));
    }

    /**
     * 文档内容变化时的处理
     * @param {TextDocumentChangeEvent} e
     *
     * @memberOf DocumentHighlight
     */
    onUpdate(document = this.document) {
        if (this.disposed || this.document.uri.toString() !== document.uri.toString()) {
            return;
        }

        // 清除之前的防抖定时器
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
        }

        // 使用防抖来避免频繁更新
        this.updateTimeout = setTimeout(() => {
            this._fullUpdate();
        }, DEBOUNCE_DELAY);
    }

    /**
     * 视口变化时的处理（滚动、窗口大小变化等）
     */
    onViewportChange() {
        if (this.disposed) {
            return;
        }

        // 清除之前的视口防抖定时器
        if (this.viewportTimeout) {
            clearTimeout(this.viewportTimeout);
        }

        // 使用较短的防抖延迟来快速响应滚动
        this.viewportTimeout = setTimeout(() => {
            this._viewportUpdate();
        }, VIEWPORT_DEBOUNCE_DELAY);
    }

    /**
     * 完整更新：重新解析所有颜色
     */
    async _fullUpdate() {
        try {
            const text = this.document.getText();
            const version = this.document.version.toString();

            // 检查文件大小，如果太大则跳过处理
            const maxFileSize = (this.viewConfig.maxFileSize || 500) * 1024; // 转换为字节
            if (text.length > maxFileSize) {
                console.debug(`文件过大 (${Math.round(text.length / 1024)}KB)，跳过颜色高亮处理`);
                return;
            }

            console.debug('执行完整颜色解析...');

            const result = await Promise.all(this.strategies.map(fn => fn(text)));

            const actualVersion = this.document.version.toString();
            if (actualVersion !== version) {
                if (process.env.COLOR_HIGHLIGHT_DEBUG) throw new Error('Document version already has changed');
                return;
            }

            // 缓存所有颜色结果
            this.allColorResults = concatAll(result);
            this.lastTextVersion = version;

            // 立即更新视口
            this._viewportUpdate();

        } catch (error) {
            console.error('颜色高亮完整更新错误:', error);
        }
    }

    /**
     * 视口更新：仅更新可见区域的颜色
     */
    _viewportUpdate() {
        if (!this.allColorResults) {
            // 如果还没有解析结果，执行完整更新
            this._fullUpdate();
            return;
        }

        try {
            const editors = this._getEditorsForDocument();
            if (editors.length === 0) {
                return;
            }

            // 获取所有可见行范围
            const visibleLines = this._getVisibleLines(editors);

            // 过滤出可见区域的颜色
            const visibleColors = this._filterVisibleColors(this.allColorResults, visibleLines);

            // 按颜色分组
            const colorRanges = groupByColor(visibleColors);

            if (this.disposed) {
                return false;
            }

            console.debug(`视口更新: ${visibleColors.length}/${this.allColorResults.length} 个颜色可见`);

            // 批量更新装饰器
            this._batchUpdateDecorations(colorRanges);

        } catch (error) {
            console.error('颜色高亮视口更新错误:', error);
        }
    }

    /**
     * 获取文档对应的编辑器
     */
    _getEditorsForDocument() {
        return window.visibleTextEditors.filter(
            editor => editor.document.uri.toString() === this.document.uri.toString()
        );
    }

    /**
     * 获取所有可见行号
     * @param {Array} editors 
     * @returns {Set} 可见行号集合
     */
    _getVisibleLines(editors) {
        const visibleLines = new Set();

        for (const editor of editors) {
            for (const range of editor.visibleRanges) {
                // 添加一些缓冲区域，确保滚动时的流畅体验
                const startLine = Math.max(0, range.start.line - 5);
                const endLine = Math.min(this.document.lineCount - 1, range.end.line + 5);

                for (let lineIndex = startLine; lineIndex <= endLine; lineIndex++) {
                    visibleLines.add(lineIndex);
                }
            }
        }

        return visibleLines;
    }

    /**
     * 过滤出可见区域内的颜色
     * @param {Array} allColors 所有颜色结果
     * @param {Set} visibleLines 可见行号集合
     * @returns {Array} 可见的颜色数组
     */
    _filterVisibleColors(allColors, visibleLines) {
        return allColors.filter(color => {
            const startPos = this.document.positionAt(color.start);
            const endPos = this.document.positionAt(color.end);

            // 检查颜色是否在可见行范围内
            for (let line = startPos.line; line <= endPos.line; line++) {
                if (visibleLines.has(line)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * 批量更新装饰器以提高性能
     * @param {Object} colorRanges 按颜色分组的范围
     */
    _batchUpdateDecorations(colorRanges) {
        const updateStack = this.decorations.keys()
            .reduce((state, color) => {
                state[ color ] = [];
                return state;
            }, {});

        // 构建更新堆栈
        for (const color in colorRanges) {
            updateStack[ color ] = colorRanges[ color ].map(item => {
                return new Range(
                    this.document.positionAt(item.start),
                    this.document.positionAt(item.end)
                );
            });
        }

        // 批量更新所有可见编辑器的装饰器
        const visibleEditors = this._getEditorsForDocument();

        if (visibleEditors.length > 0) {
            // 使用requestAnimationFrame来避免阻塞UI
            const updateBatch = () => {
                for (const color in updateStack) {
                    const decoration = this.decorations.get(color);
                    visibleEditors.forEach(editor => {
                        editor.setDecorations(decoration, updateStack[ color ]);
                    });
                }
            };

            // 在下一个动画帧执行更新
            if (typeof requestAnimationFrame !== 'undefined') {
                requestAnimationFrame(updateBatch);
            } else {
                // 降级到setTimeout
                setTimeout(updateBatch, 0);
            }
        }
    }

    dispose() {
        this.disposed = true;

        // 清除防抖定时器
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
            this.updateTimeout = null;
        }

        if (this.viewportTimeout) {
            clearTimeout(this.viewportTimeout);
            this.viewportTimeout = null;
        }

        this.decorations.dispose();
        this.listner.dispose();

        this.decorations = null;
        this.document = null;
        this.colors = null;
        this.listner = null;
        this.allColorResults = null;
    }
}

function groupByColor(results) {
    return results
        .reduce((collection, item) => {
            if (!collection[ item.color ]) {
                collection[ item.color ] = [];
            }

            collection[ item.color ].push(item);

            return collection;
        }, {});
}

function concatAll(arr) {
    return arr.reduce((result, item) => result.concat(item), []);
}
