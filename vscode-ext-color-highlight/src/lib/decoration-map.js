'use strict';
import vscode from 'vscode';
import { getColorContrast } from './dynamic-contrast';

// 最大装饰器数量限制，防止性能问题
const MAX_DECORATIONS = 100;

/**
 * 检测颜色是否包含透明度
 * @param {string} color 颜色值
 * @returns {boolean} 是否包含透明度
 */
function hasTransparency(color) {
    if (!color || typeof color !== 'string') return false;

    // RGBA格式检测
    const rgbaMatch = color.match(/rgba?\(([^)]+)\)/);
    if (rgbaMatch) {
        const parts = rgbaMatch[ 1 ].split(',').map(p => p.trim());
        if (parts.length === 4) {
            const alpha = parseFloat(parts[ 3 ]);
            return alpha < 1;
        }
    }

    // HSLA格式检测
    const hslaMatch = color.match(/hsla?\(([^)]+)\)/);
    if (hslaMatch) {
        const parts = hslaMatch[ 1 ].split(',').map(p => p.trim());
        if (parts.length === 4) {
            const alpha = parseFloat(parts[ 3 ]);
            return alpha < 1;
        }
    }

    // 十六进制ARGB格式检测（8位）
    if (color.startsWith('#') && color.length === 9) {
        const alpha = parseInt(color.substr(1, 2), 16);
        return alpha < 255;
    }

    // 十六进制ARGB格式检测（4位）
    if (color.startsWith('#') && color.length === 5) {
        const alpha = parseInt(color.substr(1, 1), 16);
        return alpha < 15;
    }

    return false;
}

/**
 *
 * @export
 * @class DecorationMap
 *
 * @property {{
 *  markRuler: boolean,
 *  markerType: string
 * }} options
 */
export class DecorationMap {
    /**
     * Creates an instance of DecorationMap.
     * @param {{
     *  markRuler: boolean,
     *  markerType: string
     * }} options
     *
     * @memberOf DecorationMap
     */
    constructor(options) {
        this.options = Object.assign({}, options);
        this._map = new Map();
        this._keys = [];
        this._lruColors = []; // 最近使用的颜色列表，用于LRU淘汰
    }

    /**
     * @param {string} color
     * @returns vscode.TextEditorDecorationType
     */
    get(color) {
        if (!this._map.has(color)) {
            // 检查是否超过最大装饰器数量限制
            if (this._map.size >= MAX_DECORATIONS) {
                this._evictOldestDecoration();
            }

            let rules = {};
            if (this.options.markRuler) {
                rules = {
                    overviewRulerColor: color
                };
            }

            // 先检查是否是透明颜色，为后续处理做准备
            const isTransparent = hasTransparency(color);

            switch (this.options.markerType) {
                case 'outline':
                    rules.border = `3px solid ${color}`;
                    break;
                case 'foreground':
                    rules.color = color;
                    break;
                case 'underline':
                    rules.color = 'invalid; border-bottom:solid 2px ' + color;
                    break;
                case 'dot':
                case 'dotafter':
                case 'dot-after':
                case 'dot_after':
                    rules.after = {
                        contentText: ' ',
                        margin: '0.1em 0.2em 0 0.2em',
                        width: '0.7em',
                        height: '0.7em',
                        backgroundColor: color,
                        borderRadius: '50%'
                    };
                    // 为透明圆点添加边框指示
                    if (isTransparent) {
                        rules.after.border = '1px dashed #999';
                    }
                    break;
                case 'dotbefore':
                case 'dot-before':
                case 'dot_before':
                    rules.before = {
                        contentText: ' ',
                        margin: '0.1em 0.2em 0 0.2em',
                        width: '0.7em',
                        height: '0.7em',
                        backgroundColor: color,
                        borderRadius: '50%'
                    };
                    // 为透明圆点添加边框指示
                    if (isTransparent) {
                        rules.before.border = '1px dashed #999';
                    }
                    break;
                case 'background':
                default:
                    rules.backgroundColor = color;
                    rules.color = getColorContrast(color);
                    rules.border = `3px solid ${color}`;
                    rules.borderRadius = '3px';

                    // 为透明背景添加特殊指示
                    if (isTransparent) {
                        // 使用双边框来显示透明度
                        rules.border = `2px solid ${color}`;
                        rules.outline = '1px dashed #999';
                        rules.outlineOffset = '1px';
                    }
            }

            this._map.set(color, vscode.window.createTextEditorDecorationType(rules));
            this._keys.push(color);
        }

        // 更新LRU列表
        this._updateLRU(color);

        return this._map.get(color);
    }

    /**
     * 更新最近使用颜色列表
     * @param {string} color
     */
    _updateLRU(color) {
        const index = this._lruColors.indexOf(color);
        if (index > -1) {
            this._lruColors.splice(index, 1);
        }
        this._lruColors.unshift(color);
    }

    /**
     * 淘汰最旧的装饰器
     */
    _evictOldestDecoration() {
        if (this._lruColors.length === 0) return;

        // 找到最旧的颜色（LRU列表最后一个）
        const oldestColor = this._lruColors.pop();

        if (this._map.has(oldestColor)) {
            // 释放装饰器资源
            const decoration = this._map.get(oldestColor);
            decoration.dispose();

            // 从映射中移除
            this._map.delete(oldestColor);

            // 从keys数组中移除
            const keyIndex = this._keys.indexOf(oldestColor);
            if (keyIndex > -1) {
                this._keys.splice(keyIndex, 1);
            }
        }
    }

    keys() {
        return this._keys.slice();
    }

    dispose() {
        this._map.forEach((decoration) => {
            decoration.dispose();
        });
        this._map.clear();
        this._keys = [];
        this._lruColors = [];
    }
}
