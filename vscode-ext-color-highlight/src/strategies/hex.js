import Color from 'color';

// 更新正则表达式以支持8位十六进制颜色
const colorHex =
    /.?((?:\#|\b0x)([a-f0-9]{8}|[a-f0-9]{6}|[a-f0-9]{4}|[a-f0-9]{3}))\b/gi;

/**
 * @export
 * @param {string} text
 * @param {boolean} useARGB - 是否使用ARGB格式（透明度在前）
 * @returns {{
 *  start: number,
 *  end: number,
 *  color: string
 * }}
 */
function findHex(text, useARGB) {
    let match = colorHex.exec(text);
    let result = [];

    while (match !== null) {
        const firstChar = match[ 0 ][ 0 ];
        const matchedColor = match[ 1 ];
        const start = match.index + (match[ 0 ].length - matchedColor.length);
        const end = colorHex.lastIndex;
        let hexValue = match[ 2 ];

        // 检查颜色匹配前的符号，尝试避免在不相关的上下文中着色
        // https://github.com/sergiirocks/vscode-ext-color-highlight/issues/25
        if (firstChar.length && /\w/.test(firstChar)) {
            match = colorHex.exec(text);
            continue;
        }

        try {
            let color;

            if (useARGB === true) {
                // ARGB格式：透明度在前
                if (hexValue.length === 8) {
                    // 8位ARGB格式: AARRGGBB
                    const alpha = parseInt(hexValue.substring(0, 2), 16) / 255;
                    const rgb = hexValue.substring(2);
                    // 转换为CSS兼容的rgba格式
                    color = Color('#' + rgb).alpha(alpha).rgb().string();
                } else if (hexValue.length === 4) {
                    // 4位ARGB格式: ARGB
                    const alphaHex = hexValue.substring(0, 1);
                    const alpha = parseInt(alphaHex + alphaHex, 16) / 255;
                    const rgb = hexValue.substring(1);
                    // 转换为CSS兼容的rgba格式
                    color = Color('#' + rgb).alpha(alpha).rgb().string();
                } else if (hexValue.length === 6) {
                    // 6位RGB格式: RRGGBB (无透明度)
                    color = Color('#' + hexValue).rgb().string();
                } else if (hexValue.length === 3) {
                    // 3位RGB格式: RGB (无透明度)
                    color = Color('#' + hexValue).rgb().string();
                }
            } else {
                // RGBA格式：透明度在后或者标准RGB格式
                if (hexValue.length === 8) {
                    // 8位RGBA格式: RRGGBBAA
                    const rgb = hexValue.substring(0, 6);
                    const alpha = parseInt(hexValue.substring(6, 8), 16) / 255;
                    color = Color('#' + rgb).alpha(alpha).rgb().string();
                } else if (hexValue.length === 4) {
                    // 4位RGBA格式: RGBA
                    const rgb = hexValue.substring(0, 3);
                    const alpha = parseInt(hexValue.substring(3, 4).repeat(2), 16) / 255;
                    color = Color('#' + rgb).alpha(alpha).rgb().string();
                } else if (hexValue.length === 6) {
                    // 6位RGB格式: RRGGBB (无透明度)
                    color = Color('#' + hexValue).rgb().string();
                } else if (hexValue.length === 3) {
                    // 3位RGB格式: RGB (无透明度)
                    color = Color('#' + hexValue).rgb().string();
                }
            }

            if (color) {
                result.push({
                    start,
                    end,
                    color,
                });
            }
        } catch (e) {
            console.debug('颜色解析错误:', e.message, '值:', hexValue);
        }

        match = colorHex.exec(text);
    }

    return result;
}

/**
 * 查找ARGB格式的十六进制颜色（透明度在前）
 * @export
 * @param {string} text
 * @returns {{
 *  start: number,
 *  end: number,
 *  color: string
 * }}
 */
export async function findHexARGB(text) {
    return findHex(text, true);
}

/**
 * 查找RGBA格式的十六进制颜色（透明度在后）
 * @export
 * @param {string} text
 * @returns {{
 *  start: number,
 *  end: number,
 *  color: string
 * }}
 */
export async function findHexRGBA(text) {
    return findHex(text, false);
}
