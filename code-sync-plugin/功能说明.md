该插件实现以下功能

### 配置GitHub
1. 首次使用时，会提示设置GitHub个人访问令牌（需要repo权限）
2. 输入GitHub用户名和令牌后，插件会自动验证连接，并创建一个名为CODE-SYNC-CONFIG的仓库
3. 如需修改，可在设置中更新`code-sync.githubToken`和`code-sync.githubUsername`

### 插件菜单
1. 菜单有同步配置和恢复配置，以及插件设置三个选项，根据不同的编辑器显示的菜单名称也不同，具体显示规则为为"应用名称+配置上传"和"应用名称+配置恢复"，例如Cursor的菜单名称为"Cursor配置上传"和"Cursor配置恢复"，Windsurf的菜单名称为"Windsurf配置上传"和"Windsurf配置恢复"

### 同步配置到GitHub
1. 点击状态栏中的"code同步"图标，或运行命令`code Sync: 显示同步菜单`
2. 选择"应用名称+配置上传"选项，根据当前使用的编辑器将对应路径下的配置文件上传到GitHub仓库，并覆盖远程仓库
3. 如果GitHub仓库不存在，会自动创建一个名为CODE-SYNC-CONFIG的仓库

### 从GitHub导入配置
1. 点击状态栏中的"code同步"图标，或运行命令`code Sync: 显示同步菜单`
2. 选择"应用名称+配置恢复"选项，根据当前使用的编辑器将对应路径下的配置文件从GitHub仓库下载到当前设备，覆盖本地配置

### 插件要求
1. 不同编辑器的配置文件路径不同，在上传和下载配置时，会自动根据当前使用的编辑器选择对应的配置文件路径
2. 不同编辑器的配置文件在上传和恢复时要独立，在上传和恢复时只操作当前编辑器的文件，不能操作其他编辑器的文件。例如在Cursor上传配置时，会上传到仓库的Cursor目录下，恢复时会从仓库的Cursor目录下下载配置文件


### 不同编辑器配置文件路径
Cursor:
mcp配置文件路径:
    join(os.homedir(), ".cursor", "mcp.json")
extensions配置文件路径:
    join(os.homedir(), ".cursor", "extensions", "extensions.json")
snippets配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Cursor", "User", "snippets")
keybindings配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Cursor", "User", "keybindings.json")
settings配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Cursor", "User", "settings.json")

Windsurf:
mcp配置文件路径:
    join(os.homedir(), ".codeium", "windsurf", "mcp_config.json")
extensions配置文件路径:
    join(os.homedir(), ".windsurf", "extensions", "extensions.json")
snippets配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Windsurf", "User", "snippets")
keybindings配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Windsurf", "User", "keybindings.json")
settings配置文件路径:
    join(os.homedir(), "Library", "Application Support", "Windsurf", "User", "settings.json")