# code 配置同步插件

# Code Sync 插件

这个插件可以帮助你同步编辑器的配置文件，便于在多台设备之间同步配置。目前支持 Cursor 和 Windsurf 编辑器。

## 功能特点

### 配置 GitHub
1. 首次使用时，会提示设置 GitHub 个人访问令牌（需要 repo 权限）
2. 输入 GitHub 用户名和令牌后，插件会自动验证连接，并创建一个名为 CODE-SYNC-CONFIG 的仓库
3. 如需修改，可在设置中更新 `code-sync.githubToken` 和 `code-sync.githubUsername`

### 插件菜单
1. 菜单有同步配置和恢复配置，以及插件设置三个选项
2. 根据不同的编辑器显示的菜单名称也不同，例如 Cursor 的菜单名称为「Cursor配置上传」和「Cursor配置恢复」

### 同步配置到 GitHub
1. 点击状态栏中的「Code同步」图标，或运行命令 `Code Sync: 显示同步菜单`
2. 选择「应用名称+配置上传」选项，将对应路径下的配置文件上传到 GitHub 仓库
3. 如果 GitHub 仓库不存在，会自动创建一个名为 CODE-SYNC-CONFIG 的仓库

### 从 GitHub 导入配置
1. 点击状态栏中的「Code同步」图标，或运行命令 `Code Sync: 显示同步菜单`
2. 选择「应用名称+配置恢复」选项，将配置文件从 GitHub 仓库下载到当前设备

## 支持的配置文件

### Cursor
- MCP 配置文件
- 扩展配置文件
- 代码片段
- 键盘绑定
- 设置

### Windsurf
- MCP 配置文件
- 扩展配置文件
- 代码片段
- 键盘绑定
- 设置

## 安装方法

1. 下载插件 VSIX 文件
2. 在 VS Code / Cursor / Windsurf 中，选择「扩展」→「从 VSIX 安装...」
3. 选择下载的 VSIX 文件进行安装

## 使用方法

1. 安装插件后，点击状态栏中的「Code同步」图标
2. 首次使用时，按照提示设置 GitHub 个人访问令牌和用户名
3. 设置完成后，即可使用同步功能

## 功能特点

- 支持根据当前使用的编辑器同步以下配置内容:
  - MCP配置文件
  - 扩展插件配置
  - 代码片段
  - 快捷键配置
  - 设置配置
  - 扩展目录同步

## 使用方法

### 配置GitHub

1. 首次使用时，会提示设置GitHub个人访问令牌（需要repo权限）
2. 输入GitHub用户名和令牌后，插件会自动验证连接
3. 如需修改，可在设置中更新`code-sync.githubToken`和`code-sync.githubUsername`

### 同步配置到GitHub

1. 点击状态栏中的"code同步"图标，或运行命令`code Sync: 显示同步菜单`
2. 选择"上传配置"选项
3. 如果GitHub仓库不存在，会提示你创建一个新仓库
4. 配置将被同步到GitHub仓库

### 从GitHub导入配置

1. 点击状态栏中的"code同步"图标，或运行命令`code Sync: 显示同步菜单`
2. 选择"恢复配置"选项
3. 确认后，将从GitHub下载配置到当前设备

### 配置要同步的扩展

在设置中编辑`code-sync.extensionsList`项，添加要同步的扩展ID，以逗号分隔

## 注意事项

- 同步会覆盖目标位置的现有文件
- 为避免冲突，建议在多台设备间切换时先同步再使用
- 若遇到同步错误，插件会显示详细错误信息
- 确保GitHub令牌具有正确的仓库访问权限

## 贡献

如有问题或建议，请提交issue或PR。

## 许可证

MIT