{"name": "code-sync-plugin", "displayName": "代码配置同步", "description": "在不同设备间同步编辑器配置", "version": "1.0.0", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "cursor-sync.showSyncMenu", "title": "代码同步: 显示同步菜单"}, {"command": "cursor-sync.uploadConfig", "title": "代码同步: 上传配置"}, {"command": "cursor-sync.downloadConfig", "title": "代码同步: 恢复配置"}, {"command": "cursor-sync.showSettings", "title": "代码同步: 插件设置"}, {"command": "cursor-sync.selectEditorType", "title": "代码同步: 选择编辑器类型"}, {"command": "cursor-sync.selectCursorEditor", "title": "代码同步: 选择 Cursor 编辑器"}, {"command": "cursor-sync.selectWindsurfEditor", "title": "代码同步: 选择 Windsurf 编辑器"}], "configuration": {"title": "代码配置同步", "properties": {"cursor-sync.githubToken": {"type": "string", "default": "", "description": "GitHub个人访问令牌 (需要repo权限)"}, "cursor-sync.githubUsername": {"type": "string", "default": "", "description": "GitHub用户名"}, "code-sync.editorType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Windsurf"], "default": "", "description": "手动设置编辑器类型"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "^14.17.0", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "eslint": "^7.27.0", "glob": "^7.1.7", "mocha": "^8.4.0", "ts-loader": "^9.2.2", "typescript": "^4.3.2", "webpack": "^5.38.1", "webpack-cli": "^4.7.0"}, "dependencies": {"@octokit/rest": "^18.12.0", "@types/fs-extra": "^11.0.4", "fs-extra": "^10.1.0"}}