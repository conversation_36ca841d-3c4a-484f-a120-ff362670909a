import * as os from 'os';
import * as path from 'path';
import * as fs from 'fs-extra';
import * as vscode from 'vscode';

// 编辑器类型枚举
export enum EditorType {
    Cursor = 'Cursor',
    Windsurf = 'Windsurf',
    VSCode = 'VSCode',
    Unknown = 'Unknown'
}

// 配置文件类型
export enum ConfigType {
    MCP = 'mcp',
    Extensions = 'extensions',
    Snippets = 'snippets',
    Keybindings = 'keybindings',
    Settings = 'settings'
}

// 配置文件路径接口
interface ConfigPaths {
    [ConfigType.MCP]: string;
    [ConfigType.Extensions]: string;
    [ConfigType.Snippets]: string;
    [ConfigType.Keybindings]: string;
    [ConfigType.Settings]: string;
}

// 获取当前编辑器类型
export function getCurrentEditorType(): EditorType {
    // 记录开始时间
    const startTime = Date.now();
    
    // 尝试多种方式识别编辑器类型
    let editorType = EditorType.Unknown;
    
    try {
        // 方法0: 首先从配置中读取用户手动选择的编辑器类型
        const configEditorType = vscode.workspace.getConfiguration('code-sync').get<string>('editorType');
        if (configEditorType) {
            console.log(`从配置中读取编辑器类型: ${configEditorType}`);
            if (configEditorType === 'Cursor') {
                editorType = EditorType.Cursor;
            } else if (configEditorType === 'Windsurf') {
                editorType = EditorType.Windsurf;
            }
        }
        
        // 如果配置中没有设置编辑器类型，则尝试自动识别
        if (editorType === EditorType.Unknown) {
            // 方法1: 通过环境变量识别
            const appName = process.env.VSCODE_CLI_APP_NAME || '';
            if (appName.includes('Cursor')) {
                editorType = EditorType.Cursor;
            } else if (appName.includes('Windsurf')) {
                editorType = EditorType.Windsurf;
            }
            
            // 方法2: 如果方法1未识别成功，通过 vscode 的 env 识别
            if (editorType === EditorType.Unknown && vscode.env) {
                const appNameFromEnv = vscode.env.appName || '';
                console.log(`通过vscode.env.appName识别: ${appNameFromEnv}`);
                
                if (appNameFromEnv.includes('Cursor')) {
                    editorType = EditorType.Cursor;
                } else if (appNameFromEnv.includes('Windsurf')) {
                    editorType = EditorType.Windsurf;
                } else if (appNameFromEnv.includes('Visual Studio Code')) {
                    // 如果是 VS Code，也设置为已知类型
                    editorType = EditorType.VSCode;
                }
            }
            
            // 方法3: 通过检查特定目录存在性来判断
            if (editorType === EditorType.Unknown) {
                const homedir = os.homedir();
                const cursorPath = path.join(homedir, '.cursor');
                const windsurfPath = path.join(homedir, '.windsurf');
                
                if (fs.existsSync(cursorPath)) {
                    editorType = EditorType.Cursor;
                } else if (fs.existsSync(windsurfPath)) {
                    editorType = EditorType.Windsurf;
                }
            }
        }
    } catch (error) {
        console.error(`识别编辑器类型时出错: ${error}`);
        // 出错时不阻塞进程，继续执行
    }
    
    // 记录执行时间
    const endTime = Date.now();
    console.log(`获取编辑器类型完成，耗时: ${endTime - startTime}ms，类型: ${editorType}`);
    
    return editorType;
}

// 获取编辑器显示名称
export function getEditorDisplayName(): string {
    const editorType = getCurrentEditorType();
    
    switch (editorType) {
        case EditorType.Cursor:
            return 'Cursor';
        case EditorType.Windsurf:
            return 'Windsurf';
        case EditorType.VSCode:
            return 'VSCode';
        default:
            // 如果无法识别，默认返回 VSCode
            return 'VSCode';
    }
}

// 获取配置文件路径
export function getConfigPaths(editorType: EditorType): ConfigPaths {
    const homedir = os.homedir();
    
    // 记录开始时间
    const startTime = Date.now();
    
    let configPaths: ConfigPaths;
    
    switch (editorType) {
        case EditorType.Cursor:
            configPaths = {
                [ConfigType.MCP]: path.join(homedir, '.cursor', 'mcp.json'),
                [ConfigType.Extensions]: path.join(homedir, '.cursor', 'extensions', 'extensions.json'),
                [ConfigType.Snippets]: path.join(homedir, 'Library', 'Application Support', 'Cursor', 'User', 'snippets'),
                [ConfigType.Keybindings]: path.join(homedir, 'Library', 'Application Support', 'Cursor', 'User', 'keybindings.json'),
                [ConfigType.Settings]: path.join(homedir, 'Library', 'Application Support', 'Cursor', 'User', 'settings.json')
            };
            break;
        case EditorType.Windsurf:
            configPaths = {
                [ConfigType.MCP]: path.join(homedir, '.codeium', 'windsurf', 'mcp_config.json'),
                [ConfigType.Extensions]: path.join(homedir, '.windsurf', 'extensions', 'extensions.json'),
                [ConfigType.Snippets]: path.join(homedir, 'Library', 'Application Support', 'Windsurf', 'User', 'snippets'),
                [ConfigType.Keybindings]: path.join(homedir, 'Library', 'Application Support', 'Windsurf', 'User', 'keybindings.json'),
                [ConfigType.Settings]: path.join(homedir, 'Library', 'Application Support', 'Windsurf', 'User', 'settings.json')
            };
            break;
        default:
            // 默认使用 VS Code 的配置路径
            configPaths = {
                [ConfigType.MCP]: '',
                [ConfigType.Extensions]: path.join(homedir, '.vscode', 'extensions'),
                [ConfigType.Snippets]: path.join(homedir, 'Library', 'Application Support', 'Code', 'User', 'snippets'),
                [ConfigType.Keybindings]: path.join(homedir, 'Library', 'Application Support', 'Code', 'User', 'keybindings.json'),
                [ConfigType.Settings]: path.join(homedir, 'Library', 'Application Support', 'Code', 'User', 'settings.json')
            };
    }
    
    // 记录执行时间
    const endTime = Date.now();
    console.log(`获取配置文件路径完成，耗时: ${endTime - startTime}ms，编辑器类型: ${editorType}`);
    
    return configPaths;
}

// 检查配置文件是否存在
export function checkConfigFilesExist(editorType: EditorType): { [key in ConfigType]?: boolean } {
    const configPaths = getConfigPaths(editorType);
    const result: { [key in ConfigType]?: boolean } = {};
    
    // 记录开始时间
    const startTime = Date.now();
    
    try {
        // 检查每个配置文件是否存在
        for (const configType of Object.values(ConfigType)) {
            const configPath = configPaths[configType];
            if (configPath) {
                result[configType] = fs.existsSync(configPath);
            }
        }
    } catch (error) {
        console.error(`检查配置文件存在性时出错: ${error}`);
        // 出错时不阻止进程，继续执行
    }
    
    // 记录执行时间
    const endTime = Date.now();
    console.log(`检查配置文件存在性完成，耗时: ${endTime - startTime}ms`);
    
    return result;
}

// 获取配置文件的目录路径
export function getConfigDir(editorType: EditorType, configType: ConfigType): string {
    const configPath = getConfigPaths(editorType)[configType];
    
    // 记录开始时间
    const startTime = Date.now();
    
    let result: string;
    
    // 如果是 snippets，直接返回目录路径
    if (configType === ConfigType.Snippets) {
        result = configPath;
    } else {
        // 对于其他配置文件，返回其所在的目录
        result = path.dirname(configPath);
    }
    
    // 记录执行时间
    const endTime = Date.now();
    console.log(`获取配置文件目录完成，耗时: ${endTime - startTime}ms，路径: ${result}`);
    
    return result;
}
