import * as vscode from 'vscode';
import { SyncService } from './services/syncService';
import { EditorType, getCurrentEditorType, getEditorDisplayName } from './utils/editorConfig';

// 激活插件
export function activate(context: vscode.ExtensionContext) {
    // 记录开始时间
    const startTime = Date.now();
    
    try {
        // 创建同步服务
        const syncService = new SyncService();
        
        // 获取编辑器显示名称
        const editorName = getEditorDisplayName();
        
        // 创建状态栏项
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = '$(sync) Code同步';
        statusBarItem.tooltip = '点击显示同步菜单';
        statusBarItem.command = 'cursor-sync.showSyncMenu';
        statusBarItem.show();
        
        // 注册状态栏项
        context.subscriptions.push(statusBarItem);
        
        // 注册显示同步菜单命令
        const showSyncMenuCommand = vscode.commands.registerCommand('cursor-sync.showSyncMenu', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                // 直接显示菜单，不再尝试识别编辑器类型
                const choice = await vscode.window.showQuickPick([
                    'Cursor 配置上传',
                    'Cursor 配置恢复',
                    'Windsurf 配置上传',
                    'Windsurf 配置恢复',
                    '插件设置'
                ], {
                    placeHolder: '选择操作'
                });
                
                // 根据选择执行操作
                if (choice === 'Cursor 配置上传') {
                    // 设置编辑器类型为 Cursor
                    await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Cursor', vscode.ConfigurationTarget.Global);
                    await vscode.commands.executeCommand('cursor-sync.uploadConfig');
                } else if (choice === 'Cursor 配置恢复') {
                    // 设置编辑器类型为 Cursor
                    await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Cursor', vscode.ConfigurationTarget.Global);
                    await vscode.commands.executeCommand('cursor-sync.downloadConfig');
                } else if (choice === 'Windsurf 配置上传') {
                    // 设置编辑器类型为 Windsurf
                    await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Windsurf', vscode.ConfigurationTarget.Global);
                    await vscode.commands.executeCommand('cursor-sync.uploadConfig');
                } else if (choice === 'Windsurf 配置恢复') {
                    // 设置编辑器类型为 Windsurf
                    await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Windsurf', vscode.ConfigurationTarget.Global);
                    await vscode.commands.executeCommand('cursor-sync.downloadConfig');
                } else if (choice === '插件设置') {
                    await vscode.commands.executeCommand('cursor-sync.showSettings');
                }
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`显示同步菜单完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`显示同步菜单时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`显示同步菜单失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册上传配置命令
        const uploadConfigCommand = vscode.commands.registerCommand('cursor-sync.uploadConfig', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                await syncService.uploadConfig();
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`上传配置命令完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`上传配置命令时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`上传配置命令失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册下载配置命令
        const downloadConfigCommand = vscode.commands.registerCommand('cursor-sync.downloadConfig', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                await syncService.downloadConfig();
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`下载配置命令完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`下载配置命令时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`下载配置命令失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册显示设置命令
        const showSettingsCommand = vscode.commands.registerCommand('cursor-sync.showSettings', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                await syncService.showSettings();
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`显示设置命令完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`显示设置命令时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`显示设置命令失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册选择 Cursor 编辑器命令
        const selectCursorEditorCommand = vscode.commands.registerCommand('cursor-sync.selectCursorEditor', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                // 保存编辑器类型到配置
                await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Cursor', vscode.ConfigurationTarget.Global);
                vscode.window.showInformationMessage('已选择 Cursor 编辑器');
                
                // 更新状态栏文本
                statusBarItem.text = '$(sync) Cursor同步';
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择 Cursor 编辑器完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`选择 Cursor 编辑器时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择 Cursor 编辑器失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册选择 Windsurf 编辑器命令
        const selectWindsurfEditorCommand = vscode.commands.registerCommand('cursor-sync.selectWindsurfEditor', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                // 保存编辑器类型到配置
                await vscode.workspace.getConfiguration('code-sync').update('editorType', 'Windsurf', vscode.ConfigurationTarget.Global);
                vscode.window.showInformationMessage('已选择 Windsurf 编辑器');
                
                // 更新状态栏文本
                statusBarItem.text = '$(sync) Windsurf同步';
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择 Windsurf 编辑器完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`选择 Windsurf 编辑器时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择 Windsurf 编辑器失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册选择编辑器类型命令
        const selectEditorTypeCommand = vscode.commands.registerCommand('cursor-sync.selectEditorType', async () => {
            // 记录开始时间
            const cmdStartTime = Date.now();
            
            try {
                // 显示编辑器类型选择菜单
                const editorType = await vscode.window.showQuickPick([
                    'Cursor',
                    'Windsurf'
                ], {
                    placeHolder: '选择编辑器类型'
                });
                
                if (editorType === 'Cursor') {
                    await vscode.commands.executeCommand('cursor-sync.selectCursorEditor');
                } else if (editorType === 'Windsurf') {
                    await vscode.commands.executeCommand('cursor-sync.selectWindsurfEditor');
                }
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择编辑器类型完成，耗时: ${cmdEndTime - cmdStartTime}ms`);
            } catch (error: any) {
                console.error(`选择编辑器类型时出错: ${error}`);
                
                // 记录执行时间
                const cmdEndTime = Date.now();
                console.log(`选择编辑器类型失败，耗时: ${cmdEndTime - cmdStartTime}ms`);
            }
        });
        
        // 注册命令
        context.subscriptions.push(showSyncMenuCommand);
        context.subscriptions.push(uploadConfigCommand);
        context.subscriptions.push(downloadConfigCommand);
        context.subscriptions.push(showSettingsCommand);
        context.subscriptions.push(selectCursorEditorCommand);
        context.subscriptions.push(selectWindsurfEditorCommand);
        context.subscriptions.push(selectEditorTypeCommand);
        
        // 检查 GitHub 配置
        syncService.checkGitHubConfig().then((isValid) => {
            if (!isValid) {
                // 首次使用时，提示设置 GitHub 令牌
                vscode.window.showInformationMessage(
                    '欢迎使用 Code Sync 插件！请先设置 GitHub 个人访问令牌和用户名',
                    '现在设置'
                ).then((choice: string | undefined) => {
                    if (choice === '现在设置') {
                        vscode.commands.executeCommand('cursor-sync.showSettings');
                    }
                });
            }
        });
        
        // 记录执行时间
        const endTime = Date.now();
        console.log(`插件激活完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
        console.error(`插件激活时出错: ${error}`);
        
        // 记录执行时间
        const endTime = Date.now();
        console.log(`插件激活失败，耗时: ${endTime - startTime}ms`);
    }
}

// 停用插件
export function deactivate() {
    // 记录开始时间
    const startTime = Date.now();
    
    try {
        console.log('Code Sync 插件已停用');
        
        // 记录执行时间
        const endTime = Date.now();
        console.log(`插件停用完成，耗时: ${endTime - startTime}ms`);
    } catch (error) {
        console.error(`插件停用时出错: ${error}`);
        
        // 记录执行时间
        const endTime = Date.now();
        console.log(`插件停用失败，耗时: ${endTime - startTime}ms`);
    }
}
