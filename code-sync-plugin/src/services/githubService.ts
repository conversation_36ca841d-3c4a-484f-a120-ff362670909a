import { Octokit } from '@octokit/rest';
import { RequestError } from '@octokit/request-error';
import * as vscode from 'vscode';
import * as fs from 'fs-extra';
import * as path from 'path';

// GitHub 仓库名称
const REPO_NAME = 'CODE-SYNC-CONFIG';

export class GitHubService {
    private octokit: Octokit | undefined;
    private username: string | undefined;

    constructor() {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 从配置中获取 GitHub 令牌和用户名
            const config = vscode.workspace.getConfiguration('cursor-sync');
            const token = config.get<string>('githubToken');
            this.username = config.get<string>('githubUsername');

            // 如果令牌存在，初始化 Octokit
            if (token) {
                this.octokit = new Octokit({ auth: token });
            }
        } catch (error: any) {
            console.error(`初始化 GitHub 服务时出错: ${error}`);
            // 出错时不阻止进程，继续执行
        }

        // 记录执行时间
        const endTime = Date.now();
        console.log(`初始化 GitHub 服务完成，耗时: ${endTime - startTime}ms`);
    }

    // 检查 GitHub 配置是否有效
    public async isConfigValid(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 验证令牌是否有效
            const { data } = await this.octokit.users.getAuthenticated();
            const isValid = data.login === this.username;

            // 记录执行时间
            const endTime = Date.now();
            console.log(`验证 GitHub 配置完成，耗时: ${endTime - startTime}ms，结果: ${isValid}`);

            return isValid;
        } catch (error: any) {
            console.error(`验证 GitHub 配置时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`验证 GitHub 配置失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 检查仓库是否存在
    public async repoExists(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 检查仓库是否存在
            const { status } = await this.octokit.repos.get({
                owner: this.username,
                repo: REPO_NAME
            });

            const exists = status === 200;

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查仓库存在性完成，耗时: ${endTime - startTime}ms，结果: ${exists}`);

            return exists;
        } catch (error: any) {
            // 如果仓库不存在，会抛出 404 错误
            if (error.status === 404) {
                // 记录执行时间
                const endTime = Date.now();
                console.log(`仓库不存在，耗时: ${endTime - startTime}ms`);

                return false;
            }

            console.error(`检查仓库存在性时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查仓库存在性失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 创建仓库
    public async createRepo(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 创建仓库
            await this.octokit.repos.createForAuthenticatedUser({
                name: REPO_NAME,
                description: '编辑器配置同步仓库',
                private: true,
                auto_init: true
            });

            // 记录执行时间
            const endTime = Date.now();
            console.log(`创建仓库完成，耗时: ${endTime - startTime}ms`);

            return true;
        } catch (error: any) {
            console.error(`创建仓库时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`创建仓库失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 上传文件
    public async uploadFile(localPath: string, remotePath: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 检查本地文件是否存在
            if (!fs.existsSync(localPath)) {
                console.log(`本地文件不存在: ${localPath}`);
                return false;
            }

            // 读取文件内容
            const content = await fs.readFile(localPath);
            const contentBase64 = content.toString('base64');

            // 检查远程文件是否存在
            let sha: string | undefined;
            try {
                const { data } = await this.octokit.repos.getContent({
                    owner: this.username,
                    repo: REPO_NAME,
                    path: remotePath
                });

                if (!Array.isArray(data)) {
                    sha = data.sha;
                }
            } catch (error: any) {
                // 如果文件不存在，会抛出 404 错误，这是正常的
                if (error.status !== 404) {
                    throw error;
                }
            }

            // 创建或更新文件
            await this.octokit.repos.createOrUpdateFileContents({
                owner: this.username,
                repo: REPO_NAME,
                path: remotePath,
                message: `更新配置文件: ${remotePath}`,
                content: contentBase64,
                sha
            });

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传文件完成，耗时: ${endTime - startTime}ms，文件: ${remotePath}`);

            return true;
        } catch (error: any) {
            console.error(`上传文件时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传文件失败，耗时: ${endTime - startTime}ms，文件: ${remotePath}`);

            return false;
        }
    }

    // 上传目录
    public async uploadDirectory(localDir: string, remoteDir: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 检查本地目录是否存在
            if (!fs.existsSync(localDir)) {
                console.log(`本地目录不存在: ${localDir}`);
                return false;
            }

            // 读取目录内容
            const files = await fs.readdir(localDir);

            // 上传每个文件
            for (const file of files) {
                const localPath = path.join(localDir, file);
                const remotePath = path.posix.join(remoteDir, file);

                // 如果是目录，递归上传
                const stat = await fs.stat(localPath);
                if (stat.isDirectory()) {
                    await this.uploadDirectory(localPath, remotePath);
                } else {
                    await this.uploadFile(localPath, remotePath);
                }
            }

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传目录完成，耗时: ${endTime - startTime}ms，目录: ${remoteDir}`);

            return true;
        } catch (error: any) {
            console.error(`上传目录时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传目录失败，耗时: ${endTime - startTime}ms，目录: ${remoteDir}`);

            return false;
        }
    }

    // 下载文件
    public async downloadFile(remotePath: string, localPath: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 获取文件内容
            const { data } = await this.octokit.repos.getContent({
                owner: this.username,
                repo: REPO_NAME,
                path: remotePath
            });

            // 确保不是目录
            if (Array.isArray(data)) {
                console.log(`远程路径是目录，不是文件: ${remotePath}`);
                return false;
            }

            // 确保本地目录存在
            await fs.ensureDir(path.dirname(localPath));

            // 解码并写入文件
            if ('content' in data) {
                const content = Buffer.from(data.content, 'base64');
                await fs.writeFile(localPath, content);
            } else {
                console.error(`文件内容格式不正确: ${remotePath}`);
                return false;
            }

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载文件完成，耗时: ${endTime - startTime}ms，文件: ${remotePath}`);

            return true;
        } catch (error: any) {
            // 如果文件不存在，会抛出 404 错误
            if (error.status === 404) {
                console.log(`远程文件不存在: ${remotePath}`);
                return false;
            }

            console.error(`下载文件时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载文件失败，耗时: ${endTime - startTime}ms，文件: ${remotePath}`);

            return false;
        }
    }

    // 下载目录
    public async downloadDirectory(remoteDir: string, localDir: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 获取目录内容
            const { data } = await this.octokit.repos.getContent({
                owner: this.username,
                repo: REPO_NAME,
                path: remoteDir
            });

            // 确保是目录
            if (!Array.isArray(data)) {
                console.log(`远程路径不是目录: ${remoteDir}`);
                return false;
            }

            // 确保本地目录存在
            await fs.ensureDir(localDir);

            // 下载每个文件
            for (const item of data) {
                const remotePath = item.path;
                const localPath = path.join(localDir, path.basename(remotePath));

                if (item.type === 'dir') {
                    // 如果是目录，递归下载
                    await this.downloadDirectory(remotePath, localPath);
                } else {
                    // 下载文件
                    await this.downloadFile(remotePath, localPath);
                }
            }

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载目录完成，耗时: ${endTime - startTime}ms，目录: ${remoteDir}`);

            return true;
        } catch (error: any) {
            // 如果目录不存在，会抛出 404 错误
            if (error.status === 404) {
                console.log(`远程目录不存在: ${remoteDir}`);
                return false;
            }

            console.error(`下载目录时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载目录失败，耗时: ${endTime - startTime}ms，目录: ${remoteDir}`);

            return false;
        }
    }

    // 检查远程路径是否存在
    public async pathExists(remotePath: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 检查路径是否存在
            await this.octokit.repos.getContent({
                owner: this.username,
                repo: REPO_NAME,
                path: remotePath
            });

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查远程路径存在性完成，耗时: ${endTime - startTime}ms，路径: ${remotePath}，结果: true`);

            return true;
        } catch (error: any) {
            // 如果路径不存在，会抛出 404 错误
            if (error.status === 404) {
                // 记录执行时间
                const endTime = Date.now();
                console.log(`远程路径不存在，耗时: ${endTime - startTime}ms，路径: ${remotePath}`);

                return false;
            }

            console.error(`检查远程路径存在性时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查远程路径存在性失败，耗时: ${endTime - startTime}ms，路径: ${remotePath}`);

            return false;
        }
    }

    // 创建远程目录
    public async createDirectory(remotePath: string): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查是否已初始化 Octokit
            if (!this.octokit || !this.username) {
                return false;
            }

            // 创建一个空文件来创建目录
            await this.octokit.repos.createOrUpdateFileContents({
                owner: this.username,
                repo: REPO_NAME,
                path: `${remotePath}/.gitkeep`,
                message: `创建目录: ${remotePath}`,
                content: Buffer.from('').toString('base64')
            });

            // 记录执行时间
            const endTime = Date.now();
            console.log(`创建远程目录完成，耗时: ${endTime - startTime}ms，目录: ${remotePath}`);

            return true;
        } catch (error: any) {
            console.error(`创建远程目录时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`创建远程目录失败，耗时: ${endTime - startTime}ms，目录: ${remotePath}`);

            return false;
        }
    }

    // 更新 GitHub 配置
    public updateConfig(token: string, username: string, repoName?: string): void {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 更新 Octokit 实例
            this.octokit = new Octokit({ auth: token });
            this.username = username;

            // 记录执行时间
            const endTime = Date.now();
            console.log(`更新 GitHub 配置完成，耗时: ${endTime - startTime}ms`);
        } catch (error: any) {
            console.error(`更新 GitHub 配置时出错: ${error}`);
            // 出错时不阻止进程，继续执行
        }
    }
}
