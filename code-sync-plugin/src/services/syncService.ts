import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs-extra';
import { GitHubService } from './githubService';
import {
    EditorType,
    ConfigType,
    getCurrentEditorType,
    getEditorDisplayName,
    getConfigPaths,
    checkConfigFilesExist
} from '../utils/editorConfig';

export class SyncService {
    private githubService: GitHubService;
    private editorType: EditorType;
    private editorName: string;

    constructor() {
        // 记录开始时间
        const startTime = Date.now();

        this.githubService = new GitHubService();
        this.editorType = getCurrentEditorType();
        this.editorName = getEditorDisplayName();

        // 记录执行时间
        const endTime = Date.now();
        console.log(`初始化同步服务完成，耗时: ${endTime - startTime}ms，编辑器类型: ${this.editorType}`);
    }

    // 检查 GitHub 配置
    public async checkGitHubConfig(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查 GitHub 配置是否有效
            const isValid = await this.githubService.isConfigValid();

            // 如果配置无效，提示用户设置
            if (!isValid) {
                const setupNow = await vscode.window.showInformationMessage(
                    '请先设置 GitHub 个人访问令牌和用户名',
                    '现在设置'
                );

                if (setupNow === '现在设置') {
                    await vscode.commands.executeCommand('cursor-sync.showSettings');
                }
            }

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查 GitHub 配置完成，耗时: ${endTime - startTime}ms，结果: ${isValid}`);

            return isValid;
        } catch (error: any) {
            console.error(`检查 GitHub 配置时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查 GitHub 配置失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 检查仓库
    public async checkRepository(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查仓库是否存在
            const exists = await this.githubService.repoExists();

            // 如果仓库不存在，创建仓库
            if (!exists) {
                const createNow = await vscode.window.showInformationMessage(
                    '同步仓库不存在，是否创建？',
                    '创建'
                );

                if (createNow === '创建') {
                    const created = await this.githubService.createRepo();

                    if (!created) {
                        vscode.window.showErrorMessage('创建仓库失败');
                        return false;
                    }

                    vscode.window.showInformationMessage('创建仓库成功');
                    return true;
                }

                return false;
            }

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查仓库完成，耗时: ${endTime - startTime}ms，结果: ${exists}`);

            return true;
        } catch (error: any) {
            console.error(`检查仓库时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`检查仓库失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 确保编辑器类型已设置
    private async ensureEditorTypeRecognized(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 从配置中获取编辑器类型
            const configEditorType = vscode.workspace.getConfiguration('code-sync').get<string>('editorType');

            if (configEditorType) {
                // 更新编辑器类型
                if (configEditorType === 'Cursor') {
                    this.editorType = EditorType.Cursor;
                    this.editorName = 'Cursor';
                } else if (configEditorType === 'Windsurf') {
                    this.editorType = EditorType.Windsurf;
                    this.editorName = 'Windsurf';
                }

                // 记录执行时间
                const endTime = Date.now();
                console.log(`从配置中获取编辑器类型完成，耗时: ${endTime - startTime}ms，类型: ${this.editorType}`);

                return true;
            } else {
                vscode.window.showErrorMessage('请先选择编辑器类型');

                // 记录执行时间
                const endTime = Date.now();
                console.log(`未设置编辑器类型，耗时: ${endTime - startTime}ms`);

                return false;
            }
        } catch (error: any) {
            console.error(`确保编辑器类型已设置时出错: ${error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`确保编辑器类型已设置失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 上传配置
    public async uploadConfig(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查 GitHub 配置
            const configValid = await this.checkGitHubConfig();
            if (!configValid) {
                return false;
            }

            // 检查仓库
            const repoValid = await this.checkRepository();
            if (!repoValid) {
                return false;
            }

            // 确保编辑器类型已识别
            const editorTypeValid = await this.ensureEditorTypeRecognized();
            if (!editorTypeValid) {
                return false;
            }

            // 获取配置文件路径
            const configPaths = getConfigPaths(this.editorType);

            // 检查配置文件是否存在
            const configExists = checkConfigFilesExist(this.editorType);

            // 创建进度条
            await vscode.window.withProgress<void>({
                location: vscode.ProgressLocation.Notification,
                title: `正在上传 ${this.editorName} 配置`,
                cancellable: false
            }, async (progress: vscode.Progress<{ increment?: number; message?: string }>) => {
                // 设置进度
                progress.report({ increment: 0 });

                // 检查远程目录是否存在
                const remoteDirExists = await this.githubService.pathExists(this.editorName);
                if (!remoteDirExists) {
                    // 创建远程目录
                    await this.githubService.createDirectory(this.editorName);
                }

                progress.report({ increment: 20 });

                // 上传 MCP 配置
                if (configExists[ ConfigType.MCP ]) {
                    const localPath = configPaths[ ConfigType.MCP ];
                    const remotePath = path.posix.join(this.editorName, 'mcp.json');
                    await this.githubService.uploadFile(localPath, remotePath);
                }

                progress.report({ increment: 20 });

                // 上传扩展配置
                if (configExists[ ConfigType.Extensions ]) {
                    const localPath = configPaths[ ConfigType.Extensions ];
                    const remotePath = path.posix.join(this.editorName, 'extensions.json');
                    await this.githubService.uploadFile(localPath, remotePath);
                }

                progress.report({ increment: 20 });

                // 上传代码片段
                if (configExists[ ConfigType.Snippets ]) {
                    const localDir = configPaths[ ConfigType.Snippets ];
                    const remoteDir = path.posix.join(this.editorName, 'snippets');
                    await this.githubService.uploadDirectory(localDir, remoteDir);
                }

                progress.report({ increment: 20 });

                // 上传键盘绑定
                if (configExists[ ConfigType.Keybindings ]) {
                    const localPath = configPaths[ ConfigType.Keybindings ];
                    const remotePath = path.posix.join(this.editorName, 'keybindings.json');
                    await this.githubService.uploadFile(localPath, remotePath);
                }

                // 上传设置
                if (configExists[ ConfigType.Settings ]) {
                    const localPath = configPaths[ ConfigType.Settings ];
                    const remotePath = path.posix.join(this.editorName, 'settings.json');
                    await this.githubService.uploadFile(localPath, remotePath);
                }

                progress.report({ increment: 20, message: '完成' });
            });

            // 显示成功消息
            vscode.window.showInformationMessage(`${this.editorName} 配置上传成功`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传配置完成，耗时: ${endTime - startTime}ms`);

            return true;
        } catch (error: any) {
            console.error(`上传配置时出错: ${error}`);
            vscode.window.showErrorMessage(`上传配置失败: ${error.message || error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`上传配置失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 下载配置
    public async downloadConfig(): Promise<boolean> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 检查 GitHub 配置
            const configValid = await this.checkGitHubConfig();
            if (!configValid) {
                return false;
            }

            // 检查仓库
            const repoValid = await this.checkRepository();
            if (!repoValid) {
                return false;
            }

            // 确保编辑器类型已识别
            const editorTypeValid = await this.ensureEditorTypeRecognized();
            if (!editorTypeValid) {
                return false;
            }

            // 获取配置文件路径
            const configPaths = getConfigPaths(this.editorType);

            // 检查远程目录是否存在
            const remoteDirExists = await this.githubService.pathExists(this.editorName);
            if (!remoteDirExists) {
                vscode.window.showErrorMessage(`远程仓库中不存在 ${this.editorName} 配置`);
                return false;
            }

            // 创建进度条
            await vscode.window.withProgress<void>({
                location: vscode.ProgressLocation.Notification,
                title: `正在恢复 ${this.editorName} 配置`,
                cancellable: false
            }, async (progress: vscode.Progress<{ increment?: number; message?: string }>) => {
                // 设置进度
                progress.report({ increment: 0 });

                // 下载 MCP 配置
                const mcpRemotePath = path.posix.join(this.editorName, 'mcp.json');
                const mcpExists = await this.githubService.pathExists(mcpRemotePath);
                if (mcpExists) {
                    const localPath = configPaths[ ConfigType.MCP ];
                    await this.githubService.downloadFile(mcpRemotePath, localPath);
                }

                progress.report({ increment: 20 });

                // 下载扩展配置
                const extensionsRemotePath = path.posix.join(this.editorName, 'extensions.json');
                const extensionsExists = await this.githubService.pathExists(extensionsRemotePath);
                if (extensionsExists) {
                    const localPath = configPaths[ ConfigType.Extensions ];
                    await this.githubService.downloadFile(extensionsRemotePath, localPath);
                }

                progress.report({ increment: 20 });

                // 下载代码片段
                const snippetsRemotePath = path.posix.join(this.editorName, 'snippets');
                const snippetsExists = await this.githubService.pathExists(snippetsRemotePath);
                if (snippetsExists) {
                    const localDir = configPaths[ ConfigType.Snippets ];
                    await this.githubService.downloadDirectory(snippetsRemotePath, localDir);
                }

                progress.report({ increment: 20 });

                // 下载键盘绑定
                const keybindingsRemotePath = path.posix.join(this.editorName, 'keybindings.json');
                const keybindingsExists = await this.githubService.pathExists(keybindingsRemotePath);
                if (keybindingsExists) {
                    const localPath = configPaths[ ConfigType.Keybindings ];
                    await this.githubService.downloadFile(keybindingsRemotePath, localPath);
                }

                progress.report({ increment: 20 });

                // 下载设置
                const settingsRemotePath = path.posix.join(this.editorName, 'settings.json');
                const settingsExists = await this.githubService.pathExists(settingsRemotePath);
                if (settingsExists) {
                    const localPath = configPaths[ ConfigType.Settings ];
                    await this.githubService.downloadFile(settingsRemotePath, localPath);
                }

                progress.report({ increment: 20, message: '完成' });
            });

            // 显示成功消息
            vscode.window.showInformationMessage(`${this.editorName} 配置恢复成功，请重启编辑器以应用更改`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载配置完成，耗时: ${endTime - startTime}ms`);

            return true;
        } catch (error: any) {
            console.error(`下载配置时出错: ${error}`);
            vscode.window.showErrorMessage(`下载配置失败: ${error.message || error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`下载配置失败，耗时: ${endTime - startTime}ms`);

            return false;
        }
    }

    // 显示设置
    public async showSettings(): Promise<void> {
        // 记录开始时间
        const startTime = Date.now();

        try {
            // 获取当前设置
            const config = vscode.workspace.getConfiguration('cursor-sync');
            const token = config.get<string>('githubToken') || '';
            const username = config.get<string>('githubUsername') || '';
            const repoName = config.get<string>('githubRepo') || 'code-sync';

            // 显示设置输入框
            const newToken = await vscode.window.showInputBox({
                prompt: '输入 GitHub 个人访问令牌',
                value: token,
                password: true,
                ignoreFocusOut: true
            });

            if (newToken === undefined) {
                return;
            }

            const newUsername = await vscode.window.showInputBox({
                prompt: '输入 GitHub 用户名',
                value: username,
                ignoreFocusOut: true
            });

            if (newUsername === undefined) {
                return;
            }

            const newRepoName = await vscode.window.showInputBox({
                prompt: '输入 GitHub 仓库名称',
                value: repoName,
                ignoreFocusOut: true
            });

            if (newRepoName === undefined) {
                return;
            }

            // 更新设置
            await config.update('githubToken', newToken, true);
            await config.update('githubUsername', newUsername, true);
            await config.update('githubRepo', newRepoName, true);

            // 更新 GitHub 服务
            this.githubService.updateConfig(newToken, newUsername, newRepoName);

            // 显示成功消息
            vscode.window.showInformationMessage('设置已保存');

            // 记录执行时间
            const endTime = Date.now();
            console.log(`显示设置完成，耗时: ${endTime - startTime}ms`);
        } catch (error: any) {
            console.error(`显示设置时出错: ${error}`);
            vscode.window.showErrorMessage(`显示设置失败: ${error.message || error}`);

            // 记录执行时间
            const endTime = Date.now();
            console.log(`显示设置失败，耗时: ${endTime - startTime}ms`);
        }
    }
}
