# 插入序列 (中文版)

> **重要声明**：
> - 本插件是 [InsertNums](https://github.com/volkerdobler/insertnums) 的中文汉化版本
> - 原作者：[Volker Dobler](https://github.com/volkerdobler)，基于 [<PERSON>](https://github.com/jbrooksuk) 的 [Sublime Text插件](https://github.com/jbrooksuk/InsertNums)
> - 汉化版使用 [Cursor](https://cursor.sh/) 辅助完成，由中文社区维护
> - 原版权利和荣誉归属于原作者

在任何文本文件中插入或更改**整数**、**ASCII 字符**、**十六进制数字**、**月份名称**、**日期**或任何 **JavaScript 表达式**。
当您需要插入序列或想要根据 JavaScript 表达式更改选定的数字/字符时，这非常有用。

此扩展基于 James Books 为 Sublime Text 开发的出色扩展 (https://github.com/jbrooksuk/InsertNums)。

我过去在 Sublime Text 中大量使用这个扩展，但在 VSCode 中找不到类似灵活的扩展。我将这个 Python 扩展重写为 JavaScript 并进一步扩展它。

---

## 0.10.0 版本的新功能

我实现了一种插入日期序列的简单方法。起始日期必须采用 <年>[-<月>[-<日>]] 格式（例如 2024-4-25 表示 2024 年 4 月 25 日）。月份和日期是可选的。

作为步长，您可以决定是要添加天、周、月还是年。格式为 [:<dwmy>整数]。如果要添加 2 天，则必须输入 ":d2"。如果要减去 3 天，请使用 "d-3"。第一个字符必须是单位（天、周、月或年的首字母）。

您还可以将日期序列与频率和重复结合使用，但目前不能与表达式或停止表达式一起使用！

## 使用方法：

该扩展实现了 "插入序列" 命令，默认键绑定为 `CTRL+ALT+DOT`（在 Mac 上为 `CMD+ALT+DOT`）。（`DOT` 是键盘上的句点字符）

最简单的用法是在选择多个光标时插入以 "1" 开头的整数序列：

选择多个光标

```
|
|
|
|
|
```

按下 `CTRL+ALT+DOT` 然后 `RETURN`（默认是插入从 1 开始的数字）

```
1
2
3
4
5
```

但是可以随时更改标准行为，按下 `CTRL+ALT+DOT` 后会出现一个弹出窗口。

如果您想从整数 10 而不是 1 开始，可以这样做：
`CTRL+ALT+DOT` 然后在弹出窗口中输入 10 并按 `RETURN`，结果将是：

```
10
11
12
13
14
```

如果您在弹出窗口中在冒号 ( : ) 后添加第二个数字，则定义整数之间的步长。

要只插入每隔 5 个整数，从 5 开始，请输入：
`CTRL+ALT+DOT 5:5 RETURN`，结果将是：

```
5
10
15
20
25
```

您可以在包含数值之前使用 **~{FORMAT}**（定义见下文）对其进行格式化，
例如，输入 "`1~05d`" 将得到（从 1 开始，默认步长为 1，格式为 5 位数字，前导零 - 如果没有前导零，将包含空格）：

```
00001
00002
00003
00004
00005
```

末尾的 _d_ 表示"十进制"输出。如果需要十六进制数字，请将 d 替换为 _x_。如果需要八进制输出，在末尾加上 _o_，二进制数字可以用 _b_ 在末尾插入。

有时，您可能需要在固定数量的重复后重新开始序列。_(这是一个新功能，原始的 Sublime Text 扩展中没有包含！)_
例如，如果您只想包含数字 1、2 和 3，之后应该从 1 重新开始。
这可以通过可选的 **#{重复次数}** 实现。
输入 `1#3` 的结果是：

```
1
2
3
1
2
```

我经常需要的另一个功能是在增加值之前重复当前字符/数字几次。_(这是一个新功能，原始的 Sublime Text 扩展中没有包含！)_
例如，如果您想包含数字 1 三次，然后数字 2 三次，然后数字 3 三次（总共 9 次插入）。
这可以通过可选的 **\*{频率}** 实现。

另一种可能的需求是添加数字 1 三次，然后数字 5 三次，依此类推。同样，如果您输入 `1:4*3`，程序将插入 1, 1, 1，然后加 4，插入 5, 5, 5，再加 4，插入数字 9 三次。

也可以使用选项 **@{停止表达式}** 来设置停止条件。
_停止表达式_ 可以是任何常规的 JavaScript 代码，但有一个优势，就是可以使用一些特殊字符（详细信息请参见下面的 **语法** 章节）。

_示例：_ 完全没有多选择
开始（仅当前光标）：

```
|
```

输入 `1@_>5` 后，将插入 5 行 _(当前值大于 5 时停止)_。

```
1
2
3
4
5
```

您可以在一个命令中组合所有这些选项。
选择一个光标并使用以下命令 `3:2*2#4@i>10` 的结果是：

```
3
3
5
5
7
7
9
9
3
3
5
```

输入的顺序也很重要。默认情况下，扩展按点击顺序插入序列。示例：首先点击第 7 行，然后是第 2 行，第三行是第 4 行。执行命令 `1` 后，结果是（第一列显示行号）：

```
1:
2: 2
3:
4: 3
5:
6:
7: 1
8:
```

如果您想从上到下插入数字（与点击顺序无关），可以在命令末尾添加 `$`。与上面相同的示例但使用命令 `1$` 的结果是：

```
1:
2: 1
3:
4: 2
5:
6:
7: 3
8:
```

您可以通过配置开关 `insertOrder` 将这两种行为设置为默认。

如果在命令末尾添加 `!`，也可以反转输入。
与相同情况的示例（您在第 7、2 和 4 行点击）并插入命令 `1!`，结果是：

```
1:
2: 2
3:
4: 1
5:
6:
7: 3
8:
```

与 `$` 或配置开关 "insertOrder" 结合使用，效果如下：

```
1:
2: 3
3:
4: 2
5:
6:
7: 1
8:
```

一种特殊的整数序列是随机序列。Insertnums 可以通过 **r{UNTIL}** 选项轻松实现这一点。
_UNTIL_ 可以是一个整数或一个加号，后跟一个整数。如果没有加号，整数将确定随机范围内的最大值。如果使用加号，_UNTIL_ 值将添加到起始值（详细语法见下文）。

如果您想包含 15 到 25 之间（包括两者）的 5 个随机数字。输入以下内容：`15r25`（或替代方案 `15r+10`）。

示例（选择了 5 条多行）：

```
19
16
15
24
24
```

每次运行此命令时都会创建新的随机数字。

从 0.9 版本开始，您还可以插入月份名称（一月、二月等）或数字。要做到这一点，您需要在前面加上分号，并以已知的月份名称（例如 10 月）或 1 到 12 之间的整数开始。`<step>` 也可用于跳过一些月份。

使用名称的示例 `;Sep@i>5`：

```
Sep
Oct
Nov
Dec
Jan
Feb
```

使用整数的示例 `;9@i>5`：

```
Sep
Oct
Nov
Dec
Jan
Feb
```

如果您想更改语言，可以在月份后的方括号内提供有效的 ISO 语言（例如 `;Sep[en-GB]@i>5`）：

```
Sept
Oct
Nov
Dec
Jan
Feb
```

另一种序列是日期（不仅仅是月份名称）。要插入日期，您可以用 _%_ 开始表达式。作为步长计数器，您可以决定是添加\_天\_、\_周\_、\_月\_还是\_年\_。例如，您想插入日期，从 2024 年 4 月 25 日开始，每步增加 2 周，并以 <年><月><日> 格式输出日期，您可以输入 `%2024-4-25:w2~yyyyMMdd` 并获得以下结果：

```
20240425
20240509
20240523
20240606
20240620
20240704
```

与月份名称输入相同的结果可以通过此日期序列插入来实现：`%2024-4:m1~MMM`：

```
四月
五月
六月
七月
八月
九月
十月
```

还有一个更复杂的功能叫做"表达式"。在这样的表达式中，您可以使用一些（内部）"变量"。（参见[语法部分](#Syntaxes:)）

例如，您可以根据前一个选择插入数字（将最后一个值加倍）。您可以输入 `CTRL+ALT+DOT 1::p>0?2*p:1`（:: 后面的任何内容都被视为 JavaScript 表达式，包括内部变量的替换）

```
1
2
4
8
16
```

由于表达式中的一个"变量"是 `_`（下划线），代表选择下的当前值，您甚至可以操作当前值。

_示例：_ 您选择了一系列数字，并希望为每个数字分别添加 50。

开始（所有 5 个数字都被选中，| 显示光标）：

```
1|
2|
3|
4|
5|
```

输入 `::_+50` 后

```
51
52
53
54
55
```

但这个扩展不仅可以包含_数字_。该扩展很灵活，可以**处理 ASCII 字符**，所以与上面相同的选择，但使用 `CTRL+ALT+DOT a RETURN`

```
a
b
c
d
e
```

或者如果您想要左侧格式化字母字符：`z~<6`（冒号只是下划线，在文档中不可见的后续空格）

```
:z     :
:aa    :
:ab    :
:ac    :
:ad    :
```

最后，您还可以使用更复杂的**表达式**来插入数字、浮点数、字符串或布尔值。

例如：选择了 5 个数字 _(| 显示光标)_：

```
1|
2|
3|
4|
5|
```

使用表达式：`|if (i+1<=3) {_+100} else {_+200}`，结果将是：
_(对于前 3 个数字将加 100，对于所有其他数字将加 200)_

```
101|
102|
103|
204|
205|
```

---

## Configuration:

从 0.9 版本开始，您可以通过配置变量设置此扩展的行为：

- `insertseq.start` 起始值，如果未提供值（默认为"1"）
- `insertseq.step` 步长值，如果未提供值（默认为"1"）
- `insertseq.cast` 转换值，如果未提供值（默认为"s" - 仅适用于表达式模式）
- `insertseq.centerString` 如果字符串长度是奇数而空间是偶数，或反之，如何居中字符串（默认为"l"）
- `insertseq.language` 月份名称的语言（默认为"zh-CN"）
- `insertseq.languageFormat` 月份名称输出格式（默认为"s"（简短版本））
- `insertseq.insertOrder` 如何插入值（默认为"cursor"，按点击顺序插入序列，替代选项：'sorted'）

---

## 历史记录：

历史记录与当前打开的工作区无关，存储在VSCode的全局存储中。

# 历史记录命令

使用命令'insertseq.showHistory'（默认键盘快捷键是CTRL+ALT+,），您可以查看之前输入的命令。选择其中一个可以直接再次运行该命令。
可以使用两个配置项：

- 'insertseq.historyLimit'（默认值：30）限制历史记录中的条目数量。如果您不想限制历史记录大小，请使用0表示无限历史记录。
- 'insertseq.editHistory'（默认值：false）定义您是否需要编辑/确认从历史记录中选择的命令，或者直接运行它。
  （特别感谢[(@codeyu)](https://github.com/codeyu)提供历史记录命令的第一个版本）。

如果您在历史记录中找不到合适的命令，可以选择"新项目"，按RETURN后，您将回到正常命令，可以在输入框中输入新命令。

# 类Bash历史记录

"正常"命令（输入框）有一个类似Bash的历史记录功能。

```
!! ::= 运行上一条命令（如果有）
!<整数> ::= 运行第 <整数> 条最后的命令（如果有）（!0 和 !! 相同）
!p ::= 在 VSCode 输出通道中显示当前历史记录
!c ::= 清除当前历史记录
```

您甚至可以向此历史记录添加一些额外的命令，但无法编辑历史命令。

示例：如果您运行了前一个命令 `10:5`，并且想添加一个停止条件，可以输入 `!!@i>5` 来使用新的停止条件运行前一个命令（新命令将是 `10:5@i>5`）。

新命令（包括编辑过的命令）将作为新条目保存在历史记录中。

类 Bash 历史记录中的命令数量不受限制，但如果重新加载扩展或 VSCode，历史记录将被清除。

---

## 语法详情：

数字的语法：

```
[<起始值>][:<步长>][#<重复次数>][*<频率>][~<格式>]r[+]<随机>][::<表达式>][@<停止表达式>][$][!]

```

其中

```

<起始值> ::= 任何整数或以 0x 开头的十六进制数
<步长> ::= 任何整数（正数或负数）或以 0x 开头的十六进制数
<重复次数> ::= 任何正整数
<频率>::= 任何正整数
<格式> ::= [<填充>][<对齐>][<符号>][#][0] 任何整数 [.<精度>][<类型>]
<随机> ::= 任何整数（如果有加号，该数字将添加到<起始值>）
<表达式> ::= 任何 JavaScript 表达式，可以包含特殊字符（见下文）
<停止表达式> ::= 任何 JavaScript 表达式，可以包含特殊字符（见下文）
$ ::= 选择将被"排序"（没有此选项，新字符将按多行点击的顺序插入）
! ::= 反转输出

```

---

可以使用以下选项进行格式化：

```

<填充> ::= 除了 } 之外的任何字符
<对齐> ::= "<" 表示左对齐，">" 表示右对齐（默认），"^" 表示居中，"=" 表示右对齐，但任何符号和符号都在任何填充的左侧
<符号> ::= "-"，"+" 或 " "（空格）

# ::= 选项导致转换使用"替代形式"（参见 Python 文档）

<精度> ::= 任何正数
<类型> ::= 以下字符之一 "bcdeEfFgGnoxX%"

```

有关格式化可能性的更多详细信息，请参阅[d3-formatting文档](https://github.com/d3/d3-format#locale_format)或[Python迷你语言文档](https://docs.python.org/3.4/library/string.html#format-specification-mini-language)。

---

字母的语法：

```

<起始值>[:<步长>][#<重复次数>][\*<频率>][~<格式>][w][@<停止表达式>][$][!]

```

其中

```

<起始值> ::= 任何 ASCII 字符
<步长> ::= 任何整数（正数或负数）
<重复次数> ::= 任何正整数
<频率>::= 任何正整数
<格式> ::= [<填充>][<对齐>][<整数>]
w ::= 将输出包装为一个字符。例如，在 z 之后，不是 aa，而只是 a（最后一个字符）
<停止表达式> ::= 具有一些特殊字符的任何 JavaScript 表达式，见下文
$ ::= 选择将被"排序"（没有此选项，新字符将按多行点击的顺序插入）
! ::= 反转输出

```

---

可以使用以下选项进行格式化：

```

<填充> ::= 除了 } 之外的任何字符
<对齐> ::= "<" 表示左对齐，">" 表示右对齐，"^" 表示居中
<整数> ::= 任何正整数（字符串的长度）

```

---

日期的语法：

```

%[<年>[-<月>[-<日>]]][:[dwmy]<步长>][#<重复次数>][*<频率>][~<格式>][$][!]

```

其中

```

<年> ::= 2 位数年份或 4 位数年份
<月> ::= 1 到 12 之间的任何整数
<日> ::= 1 到 31 之间的任何整数（注意，没有对有效日期的检查，例如 31.2. 是可能的！）
[dwmy] ::= 增加或减少的单位（_d_ay 天，_w_eek 周，_m_onth 月或 _y_ear 年）
<步长> ::= 任何整数（正数或负数）
<重复次数> ::= 任何正整数
<频率>::= 任何正整数
<格式> ::= 任何有效的日期格式。内部使用 datefns.format，所以请查看 [datefns 文档](https://date-fns.org/v3.6.0/docs/format)
$ ::= 选择将被"排序"（没有此选项，新字符将按多行点击的顺序插入）
! ::= 反转输出

```

---

月份名称的语法：

```

;<起始值>[:<步长>][#<重复次数>][\*<频率>][~<格式>][@<停止表达式>][$][!]

```

其中

```

<起始值> ::= 任何月份名称的开头或 1 到 12 之间的整数
<步长> ::= 任何整数（正数或负数）
<重复次数> ::= 任何正整数
<频率>::= 任何正整数
<格式> ::= s(hort)?|l(ong)?
<停止表达式> ::= 具有一些特殊字符的任何 JavaScript 表达式，见下文
$ ::= 选择将被"排序"（没有此选项，新字符将按多行点击的顺序插入）
! ::= 反转输出

```

月份输出的格式化可以使用以下选项：

```

s(hort)? ::= 月份名称的输出是缩写（例如 1月）
l(ong)? ::= 月份名称的输出是全名（例如 一月）

```

---

表达式的语法：

```

[<转换>]|[~<格式>::]<表达式>[@<停止表达式>][$][!]

```

其中

```

<转换> ::= "i", "f", "s", "b"
<格式> ::= 与数字相同
<表达式> ::= 任何包含特殊字符的 JavaScript 表达式
<停止表达式> ::= 具有一些特殊字符的任何 JavaScript 表达式，见下文
$ ::= 选择将被"排序"（没有此选项，新字符将按多行点击的顺序插入）
! ::= 反转输出

```

_注意：您可以在表达式中使用停止表达式，但与数字不同，停止表达式不能扩展当前选择（只能在最后一个选择处停止）。如果停止表达式比选择短，其余部分将不会更改。如果您想删除其余部分，必须为表达式提供一个空字符串作为返回代码，而不是 true。_

表达式的 _"转换"_ 信息定义了输出：

```

i ::= 输出是整数
s ::= 输出是字符串（默认）
f ::= 输出是浮点数
b ::= 输出是布尔值

```

---

可以使用以下 **_特殊字符_**，它们将被某些值替换：

```

\_ ::= 当前值（表达式之前或当前选择下的值）
s ::= <步长>的值
n ::= 选择的数量
p ::= 前一个值（最后插入的）
c ::= 当前值（仅在表达式内，包括表达式后的值）
a ::= <起始值>的值
i ::= 计数器，从 0 开始，随着每次插入而增加

```

## 其他信息

有关更多示例和信息，请查看[原始扩展](https://github.com/jbrooksuk/InsertNums)。

## 发布说明

所有发布说明都在更新日志文件中

## 贡献者 🙏

非常感谢那些为改进这个项目做出贡献的人：

- Yu [(@codingyu)](https://github.com/codingyu) &mdash; [贡献](https://github.com/codingyu/insertnums) 在 0.5.0 版本中添加了历史选择列表的第一个版本

- Jesse Peden [(@JessePeden)](https://github.com/JessePeden) &mdash; [贡献](https://github.com/volkerdobler/insertnums/pull/12) 修正了 package.json 文件中的拼写错误

- Noah [(@nmay231)](https://github.com/nmay231) &mdash; 启发我实现日期序列

## 特别感谢！

如果没有 James Brooks 的原始 Python 代码 [insertnums](https://github.com/jbrooksuk/InsertNums)，这个项目将不可能实现。
我还使用了 d3 组的 [d3-format](https://github.com/d3/d3-format)。

非常感谢！
Volker

**享受使用！**
