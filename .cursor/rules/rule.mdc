---
description:
globs:
alwaysApply: true
---
---
trigger: always_on
description: vscode插件开发
---

# 📦 VSCode 插件开发与修改指南（始终使用中文）

我们在此项目中进行 VSCode 插件的开发与维护工作。请严格遵循以下开发规范与流程：

## 🗂️ 项目结构说明

- 本项目中的每一个子目录均表示一个独立的 VSCode 插件。
- 插件目录应包含清晰的结构，例如：`src/`、`package.json`、`README.md`、`resources/` 等标准模块。

## ✨ 插件创建与修改要求

1. 根据功能需求，可以：
- 新建插件（推荐使用 `yo code` 脚手架生成基础结构）
- 修改现有插件（保持已有功能的稳定性）

2. 所有插件中涉及：
- 插件名称、描述、菜单项、按钮、提示信息、界面文字、输出内容等
- **必须统一使用中文**，确保用户界面友好一致

3. 插件配置项需清晰完整：
- 支持通过 `package.json` 配置插件命令、快捷键、激活事件等
- 所有配置项添加必要的注释，说明用途和使用方式

4. 插件中的所有文字内容（包括但不限于以下内容）：
- 插件名称、简介、菜单名称、提示信息、按钮文字、配置项描述、命令名称、日志输出、界面文本
- **必须统一使用中文，禁止出现英文混杂现象**。

5. 如在维护已有插件过程中发现存在英文内容，**务必将其全部翻译并替换为中文**，确保界面、配置项和文档的中文一致性和友好性。

## ✅ 代码质量与打包要求

在提交或打包插件前，请务必完成以下步骤：

1. **代码检查**
- 检查是否有语法错误、异常逻辑或未使用的变量
- 保持代码结构清晰，适当抽象复用函数，提升可维护性
- 推荐使用 ESLint 和 Prettier 保持风格统一。

2. **功能测试**
- 本地调试插件功能是否符合预期
- 所有功能均应可在 VSCode 插件运行环境中正常使用

3. **打包发布**
- 当插件功能测试无误且无报错时，将插件打包为 `.vsix` 文件，存放在插件根目录。.vsix的文件名需要使用中文。
- 打包前确保 `README.md` 和 `CHANGELOG.md` 内容完整，方便后续维护和用户阅读

## 回复要求
Whenever you want to ask a question, always call the MCP interactive_feedback。
Whenever you’re about to complete a user request, call the MCP interactive_feedback instead of simply ending the process. Keep calling MCP until the user’s feedback is empty, then end the request.
