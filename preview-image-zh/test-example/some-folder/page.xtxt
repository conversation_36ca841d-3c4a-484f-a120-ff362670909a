<?xml version="1.0" encoding="UTF-8"?>
<root>
    <!-- 测试 page.xtxt 文件的图片预览功能 -->

    <!-- 这是一个测试文件，用于演示插件在 page.xtxt 文件中的特殊路径查找功能 -->

    <!-- 图片引用示例 -->
    <!-- 下面的图片引用会自动在上级目录的 resources/dark 和 resources/light 中查找 -->

    <ThemePage tag="设置" description="护眼模式">
        <image description="调节色温icon（护眼模式-经典护眼） ⇩" src="res/drawable-xxhdpi/temperature_icon.png" />
        <image description="纸质纹理icon（护眼模式-经典护眼） ⇩" src="res/drawable-xxhdpi/texture_icon.png" />
        <image description="测试图片1" src="icon.png" />
        <image description="测试图片2" src="background.jpg" />
    </ThemePage>
    <!-- 功能说明: 当在 page.xtxt 文件中引用图片时： 1. 插件会自动在当前文件上级目录的 resources/dark 中查找 2. 如果没找到，会在 resources/light 中查找 3. 找到的第一个匹配文件将被用于预览 这个功能特别适用于主题相关的图片资源管理。 -->
</root>