import { AbsoluteUrlMapper } from './mapper';
import { dataUrlMapper } from './dataurlmapper';
import { relativeToOpenFileUrlMapper } from './relativetoopenfilemapper';
import { relativeToWorkspaceRootFileUrlMapper } from './relativetoworkspacerootmapper';
import { simpleUrlMapper } from './simplemapper';
import { pageXtxtFileUrlMapper } from './pagextxtmapper';

export const absoluteUrlMappers: AbsoluteUrlMapper[] = [
    dataUrlMapper,
    pageXtxtFileUrlMapper,
    simpleUrlMapper,
    relativeToOpenFileUrlMapper,
    relativeToWorkspaceRootFileUrlMapper,
];
