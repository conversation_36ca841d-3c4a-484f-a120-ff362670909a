import * as path from 'path';
import * as fs from 'fs';

import { AbsoluteUrlMapper } from './mapper';
import { ImageCache } from '../util/imagecache';

interface DualThemeResult {
    darkPath?: string;
    lightPath?: string;
    preferredPath: string;
}

class PageXtxtDualThemeMapper implements AbsoluteUrlMapper {
    private workspaceFolder: string;
    private additionalSourceFolders: string[] = [];
    private paths: { [ alias: string ]: string | string[] };

    map(fileName: string, imagePath: string, additionalMetadata?: { relativeImageDir?: string }): string {
        // 只处理 page.xtxt 文件
        if (!fileName || !fileName.toLowerCase().endsWith('page.xtxt')) {
            return undefined;
        }

        // 获取当前文件的目录
        const currentFileDir = path.dirname(fileName);
        // 获取上级目录
        const parentDir = path.dirname(currentFileDir);

        // 处理图片路径，移除可能的前导斜杠或反斜杠
        const normalizedImagePath = imagePath.replace(/^[\/\\]+/, '');

        // 定义要搜索的资源目录
        const darkResourcePath = path.join(parentDir, 'resources', 'dark', normalizedImagePath);
        const lightResourcePath = path.join(parentDir, 'resources', 'light', normalizedImagePath);

        const result: DualThemeResult = {
            preferredPath: undefined
        };

        // 检查 dark 主题图片
        if (ImageCache.has(darkResourcePath) || fs.existsSync(darkResourcePath)) {
            result.darkPath = darkResourcePath;
            result.preferredPath = darkResourcePath; // 优先使用 dark 主题
        }

        // 检查 light 主题图片
        if (ImageCache.has(lightResourcePath) || fs.existsSync(lightResourcePath)) {
            result.lightPath = lightResourcePath;
            if (!result.preferredPath) {
                result.preferredPath = lightResourcePath; // 如果没有 dark 主题，使用 light 主题
            }
        }

        // 如果找到了任何图片，返回结果
        if (result.preferredPath) {
            // 为了兼容现有系统，我们仍然返回首选路径
            // 但可以通过 additionalMetadata 传递额外信息
            if (additionalMetadata) {
                (additionalMetadata as any).dualThemeResult = result;
            }
            return result.preferredPath;
        }

        return undefined;
    }

    refreshConfig(workspaceFolder: string, sourcefolders: string[], paths: { [ alias: string ]: string | string[] }) {
        this.workspaceFolder = workspaceFolder;
        this.additionalSourceFolders = sourcefolders;
        this.paths = paths;
    }
}

export const pageXtxtDualThemeMapper: AbsoluteUrlMapper = new PageXtxtDualThemeMapper(); 