import * as path from 'path';
import * as fs from 'fs';

import { AbsoluteUrlMapper } from './mapper';
import { ImageCache } from '../util/imagecache';

class PageXtxtFileUrlMapper implements AbsoluteUrlMapper {
    private workspaceFolder: string;
    private additionalSourceFolders: string[] = [];
    private paths: { [ alias: string ]: string | string[] };

    map(fileName: string, imagePath: string, additionalMetadata?: { relativeImageDir?: string }) {
        // 只处理 page.xtxt 文件
        if (!fileName || !fileName.toLowerCase().endsWith('page.xtxt')) {
            return undefined;
        }

        // 获取当前文件的目录
        const currentFileDir = path.dirname(fileName);
        // 获取上级目录
        const parentDir = path.dirname(currentFileDir);

        // 处理图片路径，移除可能的前导斜杠或反斜杠
        const normalizedImagePath = imagePath.replace(/^[\/\\]+/, '');

        // 定义要搜索的资源目录
        const darkResourcePath = path.join(parentDir, 'resources', 'dark', normalizedImagePath);
        const lightResourcePath = path.join(parentDir, 'resources', 'light', normalizedImagePath);

        const foundPaths = [];

        // 检查 dark 主题图片
        if (ImageCache.has(darkResourcePath) || fs.existsSync(darkResourcePath)) {
            foundPaths.push(`[DARK]${darkResourcePath}`);
        }

        // 检查 light 主题图片
        if (ImageCache.has(lightResourcePath) || fs.existsSync(lightResourcePath)) {
            foundPaths.push(`[LIGHT]${lightResourcePath}`);
        }

        // 如果找到了图片，使用特殊分隔符连接多个路径
        if (foundPaths.length > 0) {
            return foundPaths.join('|||');
        }

        return undefined;
    }

    refreshConfig(workspaceFolder: string, sourcefolders: string[], paths: { [ alias: string ]: string | string[] }) {
        this.workspaceFolder = workspaceFolder;
        this.additionalSourceFolders = sourcefolders;
        this.paths = paths;
    }
}

export const pageXtxtFileUrlMapper: AbsoluteUrlMapper = new PageXtxtFileUrlMapper();