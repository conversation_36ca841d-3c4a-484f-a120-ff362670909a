# 性能优化说明

## 滚动闪烁问题修复

### 问题描述

在之前的版本中，用户在滚动代码时可能会遇到图片预览闪烁的问题。这是由于以下原因造成的：

1. **频繁的事件触发**：滚动时 `onDidChangeTextEditorVisibleRanges` 事件频繁触发
2. **短延迟处理**：原来的延迟时间只有 50ms，导致过于频繁的更新
3. **装饰器重建**：每次更新都会重新创建和清理装饰器，造成视觉上的闪烁

### 优化措施

#### 1. 增加延迟时间

```typescript
// 从 50ms 增加到 300ms
throttledScan(document, 300);
```

#### 2. 智能防抖机制

```typescript
// 如果距离上次扫描时间太短，增加延迟以避免过度更新
if (lastScanTime[lookupKey] && (now - lastScanTime[lookupKey]) < 100) {
    timeout = Math.max(timeout, 500);
}
```

#### 3. 改进装饰器更新逻辑

- **检查取消状态**：避免过期请求的结果覆盖最新结果
- **批量更新**：先准备所有新装饰器，再一次性清理旧的
- **减少重绘**：避免中间状态的显示

```typescript
// 检查token是否已被取消
if (scanResult.token.token.isCancellationRequested) {
    return;
}

// 先准备新的装饰器
const newDecorations: Decoration[] = [];
// ... 创建装饰器 ...

// 然后清理旧的装饰器
clearEditorDecorations(/* ... */);

// 最后更新装饰器列表
currentScanResult.decorations = newDecorations;
```

### 效果

这些优化措施显著改善了用户体验：

1. **消除闪烁**：滚动时图片预览保持稳定
2. **提升性能**：减少不必要的计算和重绘
3. **更好的响应性**：在快速滚动和慢速滚动时都有良好表现

### 配置建议

如果用户仍然遇到性能问题，可以考虑：

1. 在大型文件中禁用实时预览
2. 调整 `imagePreviewMaxHeight` 和 `imagePreviewMaxWidth` 设置
3. 使用更简单的图片格式

这些优化确保了插件在各种使用场景下都能提供流畅的体验。 