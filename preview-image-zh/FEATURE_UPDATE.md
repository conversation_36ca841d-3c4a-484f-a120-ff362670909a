# 新功能：page.xtxt 文件的特殊路径查找支持

## 功能概述

为图片预览增强版插件添加了对 `page.xtxt` 文件的特殊路径查找功能。当在 `page.xtxt` 文件中引用图片时，插件会自动在该文件上级目录的 `resources/dark` 和 `resources/light` 目录中查找图片。

## 实现详情

### 新增文件

1. **`src/mappers/pagextxtmapper.ts`** - 新的路径映射器
   - 专门处理 `page.xtxt` 文件的图片路径查找
   - 实现了 `AbsoluteUrlMapper` 接口
   - 优先在 `resources/dark` 目录查找，然后在 `resources/light` 目录查找

### 修改文件

1. **`src/mappers/index.ts`** - 映射器索引文件
   - 导入新的 `pageXtxtFileUrlMapper`
   - 将其添加到映射器数组中，确保优先处理

2. **`README_CN.md`** - 更新文档
   - 添加新功能说明
   - 更新使用方法和示例
   - 更新版本日志

3. **`package.json`** - 更新版本号
   - 版本从 0.32.2 升级到 0.32.3

## 工作原理

```
文件结构示例：
/project
  /some-folder
    /page.xtxt          <- 当前文件
  /resources
    /dark
      /image.png        <- 优先在这里查找
    /light
      /image.png        <- 如果 dark 中没有，则在这里查找
```

当在 `page.xtxt` 文件中写入：
```xml
<ThemePage tag="设置" description="护眼模式">
    <image description="调节色温icon" src="res/drawable-xxhdpi/temperature_icon.png" />
</ThemePage>
```

插件会自动：
1. 检测到这是一个 `page.xtxt` 文件
2. 获取文件的上级目录
3. 按照 `src` 属性的完整路径在 `../resources/dark/res/drawable-xxhdpi/temperature_icon.png` 中查找
4. 如果没找到，在 `../resources/light/res/drawable-xxhdpi/temperature_icon.png` 中查找
5. 找到第一个匹配的文件用于预览

## 测试

在 `test-example/` 目录中创建了测试用例：
- `test-example/some-folder/page.xtxt` - 测试文件
- `test-example/resources/dark/` - dark 主题图片目录
- `test-example/resources/light/` - light 主题图片目录

## 兼容性

- 该功能仅对 `page.xtxt` 文件生效
- 不影响其他文件类型的现有功能
- 与现有的映射器系统完全兼容

## 版本信息

- 版本：0.32.3
- 新增功能：page.xtxt 文件特殊路径查找支持
- 向后兼容：是 