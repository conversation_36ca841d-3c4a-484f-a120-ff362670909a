# page.xtxt 双主题图片显示功能演示

## 功能概述

插件现在支持在 `page.xtxt` 文件中同时显示 Dark 和 Light 两个主题的图片。当两个主题目录中都存在相同路径的图片时，插件会在侧边栏同时显示这两个图片。

## 目录结构

```
项目根目录/
├── some-folder/
│   └── page.xtxt              # 当前编辑的文件
└── resources/
    ├── dark/
    │   └── res/
    │       └── drawable-xxhdpi/
    │           └── temperature_icon.png    # Dark 主题图片
    └── light/
        └── res/
            └── drawable-xxhdpi/
                └── temperature_icon.png    # Light 主题图片
```

## 在 page.xtxt 中的使用

在 `page.xtxt` 文件中写入：

```xml
<ThemePage tag="设置" description="护眼模式">
    <image description="调节色温icon（护眼模式-经典护眼） ⇩" src="res/drawable-xxhdpi/temperature_icon.png" />
</ThemePage>
```

## 预期效果

1. **如果只有一个主题有图片**：显示找到的那个图片
2. **如果两个主题都有图片**：同时显示两个图片，每个图片前会有主题标识
3. **在侧边栏预览**：可以看到两个图片并排显示
4. **悬停预览**：可以同时预览两个主题的图片

## 技术实现

- 映射器会返回特殊格式的路径：`[DARK]/path/to/dark/image|||[LIGHT]/path/to/light/image`
- 服务器端解析这种格式，为每个图片创建单独的 ImageInfo
- 前端显示多个图片预览

## 测试说明

使用包含的测试示例：
1. 打开 `test-example/some-folder/page.xtxt`
2. 查看 `temperature_icon.png` 的引用行
3. 应该能在侧边栏看到两个图片预览（一个来自 dark 目录，一个来自 light 目录）

这个功能特别适用于需要同时管理多个主题图片资源的项目。 