# 图片预览增强版 (中文版) - Visual Studio Code 扩展

> **重要声明**：
> - 本插件是 [vscode-gutter-preview](https://github.com/kisstkondoros/gutter-preview) 的中文汉化增强版本
> - 原作者：[Tamas Kiss](https://github.com/kisstkondoros)
> - 汉化版使用 [Cursor](https://cursor.sh/) 辅助完成，由中文社区维护
> - 原版权利和荣誉归属于原作者

> 这是 vscode-gutter-preview 的独立中文分支版本，添加了额外的功能支持，不会被原版插件更新覆盖。

在代码行号区域和悬停时预览图片，支持变量引用和动态图片选择。

## 版本说明

这是一个独立维护的中文版本，在原版基础上增加了以下特性：

1. 完整的中文界面
2. 支持变量引用方式的图片路径
3. 支持动态图片选择（srcid）
4. 不会被原版插件的更新覆盖

## 与原版区别

1. 插件ID不同：`vscode-gutter-preview-cn`（原版为 `vscode-gutter-preview`）
2. 支持更多图片引用方式
3. 完整的中文支持
4. 独立的版本号管理

## 功能特点

1. 支持在代码行号区域预览图片
2. 支持鼠标悬停时预览图片
3. 支持动态图片选择（通过 srcid）
4. 支持变量引用方式的图片路径
5. 支持 page.xtxt 文件的特殊路径查找

## 安装方法

[如何安装 Visual Studio Code 扩展](https://code.visualstudio.com/docs/editor/extension-gallery)

## 使用方法

### 1. 基本图片预览

```html
<img src="path/to/image.png" />
```

### 2. 动态图片选择

使用 srcid 属性来选择不同的图片：

```html
<image src="bg.png" srcid="1" />
<!-- 预览 bg_1.png -->
<img src="bg.png" srcid="2" />
<!-- 预览 bg_2.png -->
<div src="bg.png" srcid="abc" />
<!-- 预览 bg_0.png -->
```

### 3. 变量引用方式

使用变量定义和引用来指定图片路径：

```xml
<Var name="customize_dev_icon" expression="'icon/icon_1.png'" type="string" const="true" />
<Image srcExp="@customize_dev_icon" ... />
```

### 4. page.xtxt 文件的特殊路径查找

对于 `page.xtxt` 文件，插件会自动在该文件上级目录的 `resources/dark` 和 `resources/light` 目录中查找图片：

```
项目结构：
/project
  /some-folder
    /page.xtxt          <- 当前文件
  /resources
    /dark
      /res
        /drawable-xxhdpi
          /temperature_icon.png    <- 优先在这里查找
          /texture_icon.png
    /light
      /res
        /drawable-xxhdpi
          /temperature_icon.png    <- 然后在这里查找
          /texture_icon.png
```

在 `page.xtxt` 文件中：
```xml
<ThemePage tag="设置" description="护眼模式">
    <image description="调节色温icon" src="res/drawable-xxhdpi/temperature_icon.png" />
    <image description="纹理icon" src="res/drawable-xxhdpi/texture_icon.png" />
</ThemePage>
```

插件会按照 `src` 属性中的完整路径在 `resources/dark/` 和 `resources/light/` 目录中查找文件：
- `src="res/drawable-xxhdpi/xxx.png"` → 查找 `resources/dark/res/drawable-xxhdpi/xxx.png`
- `src="images/icon.png"` → 查找 `resources/dark/images/icon.png`

**多主题支持**：
1. 同时在 `resources/dark/` 和 `resources/light/` 目录中查找图片
2. 如果两个目录都有相同的图片，会同时显示两个图片
3. 在侧边栏可以看到 Dark 和 Light 两个主题的图片预览

## 配置选项

1. `gutterpreview.showImagePreviewOnGutter`: 是否在代码行号区域显示预览（默认：true）
2. `gutterpreview.showUnderline`: 是否为图片URL添加下划线（默认：true）
3. `gutterpreview.imagePreviewMaxHeight`: 预览图片的最大高度（默认：100）
4. `gutterpreview.imagePreviewMaxWidth`: 预览图片的最大宽度（默认：-1）
5. `gutterpreview.sourceFolder`: 解析相对路径时的额外文件夹
6. `gutterpreview.currentColorForSVG`: SVG预览的默认颜色（默认：white）

## 支持的文件类型

-   HTML/XML
-   CSS/SCSS/LESS
-   Markdown
-   任何包含图片URL的文本文件

## 特殊功能

1. 动态图片选择：

    - 使用 srcid 属性指定图片序号
    - 自动处理数字和非数字的 srcid 值

2. 变量引用支持：
    - 支持通过 `<Var>` 标签定义变量
    - 使用 `@变量名` 方式引用变量
    - 自动处理引号和路径格式

3. page.xtxt 文件特殊路径支持：
    - 自动在上级目录的 `resources/dark` 和 `resources/light` 中查找图片
    - 按照 `src` 属性的完整路径在资源目录中查找文件
    - 支持同时显示两个主题的图片（如果两个目录都有相同的图片）
    - 保持与 `src` 属性相同的目录结构

## 注意事项

1. 变量引用目前仅在同一文件内有效
2. srcid 为非数字时默认使用 \_0 后缀
3. 图片路径支持相对路径和绝对路径

## 常见问题

Q: 为什么有些图片无法预览？
A: 检查图片路径是否正确，以及文件是否存在。对于特殊URL格式，可以通过 `gutterpreview.urlDetectionPatterns` 配置正则表达式来支持。

Q: 如何修改预览图片的大小？
A: 通过 `gutterpreview.imagePreviewMaxHeight` 和 `gutterpreview.imagePreviewMaxWidth` 配置项来调整。

## 更新日志

### 0.32.7

-   修复 VSCode 版本兼容性问题
-   降低最低版本要求至 VSCode 1.88.0
-   支持更广泛的 VSCode 版本

### 0.32.6

-   修复代码滚动时图片闪烁问题
-   优化滚动事件处理，增加防抖机制
-   改进装饰器更新逻辑，减少不必要的重绘

### 0.32.5

-   实现真正的双主题图片显示功能
-   page.xtxt 文件现在可以同时显示 dark 和 light 两个主题的图片
-   在侧边栏可以同时看到两个目录的图片预览

### 0.32.4

-   优化 page.xtxt 文件的图片查找逻辑
-   明确查找优先级：优先 resources/dark，然后 resources/light
-   完善双主题支持的基础架构

### 0.32.3

-   添加对 page.xtxt 文件的特殊路径查找支持
-   自动在上级目录的 resources/dark 和 resources/light 中查找图片

### 0.32.2

-   添加对变量引用的支持
-   添加动态图片选择功能
-   优化代码结构和性能
-   添加中文支持

## 许可证

基于 MIT 许可证开源
